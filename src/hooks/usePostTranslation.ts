import { useState, useCallback, useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';
import { Post } from '../state';

export const usePostTranslation = (item: Post | null) => {
  const router = useRouter();
  const [isTranslating, setIsTranslating] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');

  // 对 pinned posts 和官方账号的 posts 启用翻译功能
  const OFFICIAL_ACCOUNT_ID = process.env.OFFICIAL_ACCOUNT_ID
  const isTranslationEnabled = Boolean(item?.isPinned) || (item?.authUserId === OFFICIAL_ACCOUNT_ID);

  useEffect(() => {
    // Prioritize router.locale (from URL) over localStorage for consistency with i18n routing
    const lang = router.locale || localStorage.getItem('lang') || 'en';
    setCurrentLanguage(lang);
  }, [router.locale]);

  // Listen for language change events from AvatarSetting component
  useEffect(() => {
    const handleLanguageChange = (event: CustomEvent) => {
      const newLanguage = event.detail;
      setCurrentLanguage(newLanguage);
    };

    window.addEventListener('languageChanged', handleLanguageChange as EventListener);

    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange as EventListener);
    };
  }, []);

  // Get display content (translated or original) - only for pinned posts
  const displayContent = useMemo(() => {
    const hasTranslation = isTranslationEnabled && item?.translations?.[currentLanguage];
    console.log(`[usePostTranslation] Post ${item?.id}:`, {
      currentLanguage,
      isTranslationEnabled,
      hasTranslation: !!hasTranslation,
      availableLanguages: item?.translations ? Object.keys(item.translations) : [],
      translations: item?.translations
    });

    return {
      title: (isTranslationEnabled && item?.translations?.[currentLanguage]?.title) || item?.title || '',
      content: (isTranslationEnabled && item?.translations?.[currentLanguage]?.content) || item?.content || '',
    };
  }, [isTranslationEnabled, item?.translations, currentLanguage, item?.title, item?.content]);

  const handleTranslate = useCallback(async () => {
    if (isTranslating || !isTranslationEnabled || !item) return;

    setIsTranslating(true);
    try {
      const response = await fetch(`/api/translatePost?postId=${item.id}&language=${currentLanguage}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.translations && result.translations.length > 0 && item) {
          const translation = result.translations[0];
          if (!item.translations) {
            item.translations = {};
          }
          item.translations[currentLanguage] = {
            title: translation.title,
            content: translation.content,
          };
          // Force re-render by updating the item reference
          Object.assign(item, { ...item });
        }
      }
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      setIsTranslating(false);
    }
  }, [item, currentLanguage, isTranslating]);

  return {
    currentLanguage,
    displayContent,
    isTranslating,
    handleTranslate,
  };
};