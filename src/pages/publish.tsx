import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Input,
  Textarea,
  Button,
  NextUIProvider,
  Divider,
  Chip,
  Checkbox,
} from '@nextui-org/react';
import {
  appStateAtom,
  CNodeType,
  exportImageAtom,
  postContentAtom,
  postGeneratedImageUrlsAtom,
  postTitleAtom,
  authAtom,
  loginModalAtom,
  profileAtom,
} from '../state';
import { Header } from '../Components/Header';
import { useAtom, useAtomValue } from 'jotai';
import { Analytics } from '@vercel/analytics/react';
import { addWatermark, cropImage, downloadURI } from '../utilities';
import { useRouter } from 'next/router';
import { v4 as uuidv4 } from 'uuid';
import toast from 'react-hot-toast';
import mixpanel from 'mixpanel-browser';
import Head from 'next/head';
import { HiOutlineDownload } from 'react-icons/hi';
import { IconX, IconCloudUpload, IconShare3 } from '@tabler/icons-react';
import { Sidebar } from '../Components/Sidebar';
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';
import {
  uploadFile,
  migrateVideoFromReplicate,
  shareLink,
} from '@/utils/index';
import { TagSelector, type Tag } from '../Components/TagSelector';
import { useLoginModal } from '../hooks/useLoginModal';

import { FiImage, FiVideo } from 'react-icons/fi';
import { compressMedia } from '../utilities/mediaCompression';
import { cleanQualityModifiers } from '../utilities/promptUtils';
import { RiPushpinLine, RiTranslate2 } from 'react-icons/ri';

export const handlePublish = async (
  postTitle: string,
  postContent = '',
  exportMedia: string,
  postGeneratedImageUrls: any = [],
  setLoading?: any,
  // 标记展示的tag, 后期会废弃
  tags: string[] = [],
  hideMainFeed = false,
  showToast = true,
  t?: TFunction | any,
  mediaType: 'image' | 'video' | 'text' = 'image',
  pendingFile?: File | null,
  // 用于筛选分类的tag
  newTags?: Tag[],
  // 是否置顶
  isPinned = false,
  // 是否翻译
  translatePost = false,
  // eslint-disable-next-line max-params
) => {
  setLoading?.(true);

  let mediaUrls: string[] = [];

  // 如果有待上传的文件，先压缩再上传
  if (pendingFile) {
    try {
      // 显示压缩进度提示
      const compressionToast = toast.loading(
        t ? t('toast:info.compressing') : 'Compressing file...',
      );

      // 压缩文件
      const compressedFile = await compressMedia(pendingFile, {
        maxWidth: 1080,
        maxHeight: 1080,
        imageQuality: 0.8,
        videoBitrate: 1500000, // 降低视频码率
        audioBitrate: 128000,
      });

      // 更新toast为上传状态
      toast.loading(t ? t('toast:info.uploading') : 'Uploading file...', {
        id: compressionToast,
      });

      const isVideo = compressedFile.type.startsWith('video/');
      const extension = isVideo ? '.webm' : '.webp';
      const imagePath = `app_media/${uuidv4()}${extension}`;

      const uploadResult = await uploadFile(imagePath, compressedFile);

      if (!uploadResult) {
        throw new Error('Failed to upload file');
      }

      // 使用上传后的URL作为exportMedia
      exportMedia = uploadResult;

      // 关闭loading toast
      toast.dismiss(compressionToast);
    } catch (error) {
      setLoading?.(false);
      console.error('Failed to compress or upload file:', error);
      if (t) {
        toast.error(t('toast:error.failedToProcessFile'));
      } else {
        toast.error('Failed to process file. Please try again.');
      }
      return null;
    }
  }

  if (mediaType === 'image' && !pendingFile) {
    // 只有当不是用户上传的文件时，才进行cropImage处理
    const cropedImages = await cropImage(exportMedia);

    const uploadImage = async (image: string) => {
      const blob = await fetch(image).then(res => res.blob());
      const imagePath = `app_media/${uuidv4()}.webp`;
      const file = new File([blob], imagePath);
      const result = await uploadFile(imagePath, file);
      if (result) {
        return result;
      }
      return null;
    };

    const imageUploadPromises = cropedImages.map(image => uploadImage(image));
    const results = await Promise.all(imageUploadPromises);
    mediaUrls = results.filter((url): url is string => url !== null);
  } else if (pendingFile) {
    mediaUrls = [exportMedia];
  } else if (mediaType === 'video') {
    // 检查并转存replicate.delivery的视频
    try {
      const migratedVideoUrl = await migrateVideoFromReplicate(exportMedia);
      mediaUrls = [migratedVideoUrl];
    } catch (error) {
      console.error('Failed to migrate video:', error);
      // 如果转存失败，使用原始URL
      mediaUrls = [exportMedia];
    }
  } else if (mediaType === 'text') {
    // 纯文本内容，不需要媒体文件
    mediaUrls = [];
  }

  // Represent AI generated images as url path
  const generations: string[] = [];
  for (const url of postGeneratedImageUrls) {
    if (url && url.startsWith(process.env.NEXT_PUBLIC_SUPABASE_URL!)) {
      const path = url.replace(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/husbando-land/image_generation/`,
        '',
      );
      generations.push(path);
    }
  }

  try {
    mixpanel.track('visit.page.publish', {
      title: postTitle,
      content: postContent,
      media: mediaUrls,
      type: mediaType,
    });
  } catch {
    // ignore tracking errors
  }
  const payload = {
    title: postTitle,
    description: postContent,
    images: mediaUrls,
    generations,
    tags,
    hide_main_feed: hideMainFeed,
    media_type: mediaType,
    new_tags: newTags,
    is_pinned: isPinned,
    translate_post: translatePost,
  };
  try {
    const response = await fetch('/api/postStory', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error);
    }
    const { message, postId } = await response.json();
    setLoading?.(false);
    if (message && showToast) {
      toast.success(message);
    }
    return postId;
  } catch (error) {
    setLoading(false);
    console.error('Error posting:', error);
    if (t) {
      toast.error(t('toast:error.failedPost'));
    } else {
      toast.error('Failed to post. Please try again.');
    }
    return null;
  }
};

const promptToTags = (prompt: string) => {
  const tags = prompt.split(',').map(tag => tag.trim());
  const presetPrompt =
    'best quality, 4k, masterpiece, highres, detailed, amazing quality, rating: general';
  const tagsNeedToDelete = presetPrompt.split(',').map(tag => tag.trim());
  const characterIdMatch = prompt.match(/<[^>]+>/);
  let characterId = '';
  if (characterIdMatch) {
    characterId = characterIdMatch[0];
  }
  const tagsToAdd = tags.filter(
    tag =>
      tag.length > 0 && tag.length <= 20 && !tagsNeedToDelete.includes(tag),
  );
  let finalTags = tagsToAdd.slice(0, 10);
  if (characterId && !finalTags.some(tag => tag === characterId)) {
    finalTags = finalTags.slice(0, 9);
    finalTags.push(characterId);
  }
  return finalTags.map(name => ({
    name,
    id: -1,
  }));
};

const contentToTags = (content: string) => {
  if (!content) {
    return [];
  }
  const characterIdMatch = /\/character\/(.+?)\s/.exec(content);
  let characterId = '';
  if (characterIdMatch) {
    characterId = characterIdMatch[1];
  }
  const tags = content
    .split('\n')
    .map(line => line.trim().replace(/^.+?:/, '').trim())
    .filter(line => line.length > 0)
    .flatMap(line => line.split(','))
    .filter(line => line.length > 0 && line.length <= 20);

  let result = tags.slice(0, 10).map(name => ({
    name,
    id: -1,
  }));
  if (characterId) {
    result = result.slice(0, 9);
    result.push({ name: `<${characterId}>`, id: -1 });
  }
  return result;
};

const PublishWorkComponent: React.FC = () => {
  const { t } = useTranslation(['publish', 'toast']);
  const [exportImage, setExportImage] = useAtom(exportImageAtom);
  const [postGeneratedImageUrls, setPostGeneratedImageUrls] = useAtom(
    postGeneratedImageUrlsAtom,
  );
  const [postTitle, setPostTitle] = useAtom(postTitleAtom);
  const [postContent, setPostContent] = useAtom(postContentAtom);
  const [isAuth] = useAtom(authAtom);
  const [loginModalState] = useAtom(loginModalAtom);
  const profile = useAtomValue(profileAtom);
  const { LoginModal } = useLoginModal();
  const [loading, setLoading] = useState(false);
  const [mediaType, setMediaType] = useState<'image' | 'video' | 'text'>(
    'image',
  );
  const [urlParamsProcessed, setUrlParamsProcessed] = useState(false);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [pendingFile, setPendingFile] = useState<File | null>(null); // 新增：待上传的文件
  const [pinPost, setPinPost] = useState(false);
  const [translatePost, setTranslatePost] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const appState = useAtomValue(appStateAtom);

  // 检查是否是官方账号
  const isOfficialAccount = profile.is_official || false;

  // 清除URL查询参数但保持状态
  const clearUrlQuery = async () => {
    const { pathname } = router;
    await router.replace(pathname, undefined, { shallow: true });
  };

  // 清除所有内容的函数
  const clearAllContent = () => {
    if (exportImage && exportImage.startsWith('blob:')) {
      URL.revokeObjectURL(exportImage);
    }

    setExportImage('data:,');
    setPostGeneratedImageUrls([]);
    setPostTitle('');
    setPostContent('');
    setSelectedTags([]);
    setUrlParamsProcessed(false);
    setMediaType('image');
    setPendingFile(null);
    setPinPost(false);
    setTranslatePost(false);
  };

  useEffect(
    () => () => {
      if (exportImage && exportImage.startsWith('blob:')) {
        URL.revokeObjectURL(exportImage);
      }
    },
    [exportImage],
  );

  // 自动清理之前的内容
  useEffect(() => {
    if (!router.isReady) {
      return;
    }

    // 检查是否有之前留存的内容（不是来自URL参数）
    const hasContent =
      (exportImage && exportImage !== 'data:,') || postTitle || postContent;
    const hasUrlParams = router.query.mediaUrl;
    const hasContentFromCreate =
      exportImage?.startsWith('data:') && exportImage.length > 'data:,'.length;

    if (hasContentFromCreate) {
      return;
    }
    // 如果有内容但不是来自URL参数，自动清除
    if (hasContent && !hasUrlParams) {
      clearAllContent();
    }
  }, [router.isReady]);

  // 处理URL参数
  useEffect(() => {
    if (!router.isReady) {
      return;
    }

    const { mediaUrl, mediaType, prompt, content, tags } = router.query;
    // 从character/[character_id]页面跳转过来的，单独加OC tag <character-id>
    // 并把content中的tag也加进去
    if (tags && typeof tags === 'string') {
      const contentTags = contentToTags(content as string);
      setSelectedTags(selectedTags => {
        let newTags = tags.split(',').map(tag => ({
          name: tag,
          id: -1,
        }));
        newTags = [...newTags, ...contentTags];
        const appendTags: Tag[] = [];
        for (const tag of newTags) {
          if (!selectedTags.find(t => t.name === tag.name)) {
            appendTags.push(tag);
          }
        }
        if (!appendTags.length) {
          return selectedTags;
        }
        return [...selectedTags, ...appendTags];
      });
    }

    // 如果没有URL参数，重置处理状态
    if (!mediaUrl) {
      setUrlParamsProcessed(false);
      return;
    }

    // 如果已经处理过相同的URL，跳过
    if (urlParamsProcessed && exportImage === mediaUrl) {
      return;
    }

    // 设置媒体类型
    if (mediaType && typeof mediaType === 'string') {
      setMediaType(mediaType as 'image' | 'video' | 'text');
    }

    // 如果URL中有mediaUrl，则设置exportImage（覆盖现有内容）
    if (mediaUrl && typeof mediaUrl === 'string') {
      setExportImage(mediaUrl);

      // 如果是从生成工具传过来的图片，添加到生成图片列表中
      if (mediaUrl.includes('supabase.co')) {
        setPostGeneratedImageUrls(prev => [...prev, mediaUrl]);
      }

      // if prompt is in the url, set it as title
      if (prompt && typeof prompt === 'string') {
        const cleanedPrompt = cleanQualityModifiers(prompt);
        setPostTitle(cleanedPrompt);
        if (!tags) {
          setSelectedTags(prev => {
            const newTags = promptToTags(prompt);
            console.log(newTags, 'newTags');
            const appendTags: Tag[] = [];
            for (const tag of newTags) {
              if (!selectedTags.find(t => t.name === tag.name)) {
                appendTags.push(tag);
              }
            }
            return [...prev, ...appendTags];
          });
        }
      }

      // if content is in the url, set it as post content
      if (content && typeof content === 'string') {
        const cleanedContent = cleanQualityModifiers(content);
        setPostContent(cleanedContent);
      }

      // 标记为已处理并清除URL参数
      setUrlParamsProcessed(true);
      setTimeout(() => {
        clearUrlQuery();
      }, 100);
    }
  }, [
    router.isReady,
    router.query,
    exportImage,
    setExportImage,
    setPostGeneratedImageUrls,
    postTitle,
    setPostTitle,
    urlParamsProcessed,
  ]);

  // 处理从create页面跳转过来的情况
  useEffect(() => {
    if (!router.isReady) {
      return;
    }

    // 如果没有URL参数，说明是从create页面跳转过来的
    if (
      !router.query.mediaUrl &&
      !router.query.prompt &&
      !router.query.content
    ) {
      // 从create页面过来的，需要从appState中获取prompt
      // const prompt = appState.find(node => node.attrs.text).join('\n');
      const found = appState.find(
        node => node.cType === CNodeType.COMIC_IMAGE && node.attrs.prompt,
      );
      const prompt = found?.attrs.prompt;
      if (prompt && !postTitle) {
        const cleanedPrompt = cleanQualityModifiers(prompt.trim());
        setPostTitle(cleanedPrompt);
        setSelectedTags(promptToTags(prompt));
      }
    }
  }, [router.isReady, router.query, appState, postTitle, setPostTitle]);

  // 页面离开时清理状态
  useEffect(() => {
    const handleBeforeUnload = () => {
      // 如果没有从URL参数加载内容，且内容没有保存，则清理
      if (
        !router.query.mediaUrl &&
        (exportImage !== 'data:,' || postTitle || postContent)
      ) {
        clearAllContent();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [router.query.mediaUrl, exportImage, postTitle, postContent]);

  // 处理文件上传
  const handleFileSelect = async (file: File) => {
    if (!file) {
      return;
    }

    // 检查文件类型
    const isImage = file.type.startsWith('image/');
    const isVideo = file.type.startsWith('video/');

    if (!isImage && !isVideo) {
      toast.error(t('toast:error.unsupportedFileType'));
      return;
    }

    const fileType = isVideo ? 'video' : 'image';
    setMediaType(fileType);

    setPendingFile(file);

    const previewUrl = URL.createObjectURL(file);
    setExportImage(previewUrl);
  };

  // 处理拖拽
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  // 处理文件选择
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  // 处理分享
  const handleShare = async () => {
    if (!postTitle) {
      toast.error(t('pleaseEnterTitle'));
      return;
    }

    const shareTitle = postTitle;
    const shareText = postContent || postTitle;

    if (pendingFile) {
      toast.error(t('pleasePublishFirst'));
      return;
    }

    // 如果有图片/视频，尝试分享媒体文件
    if (navigator.share) {
      try {
        if (mediaType === 'video') {
          // 分享视频文件
          const response = await fetch(exportImage);
          const blob = await response.blob();
          const file = new File([blob], 'Komiko.mp4', {
            type: 'video/mp4',
          });

          await navigator.share({
            title: shareTitle,
            text: shareText,
            files: [file],
          });
        } else {
          // 分享带水印的图片
          const watermarkedImage = await addWatermark(exportImage);
          const response = await fetch(watermarkedImage);
          const blob = await response.blob();
          const file = new File([blob], 'Komiko.jpg', {
            type: blob.type,
          });

          await navigator.share({
            title: shareTitle,
            text: shareText,
            files: [file],
          });
        }
        toast.success(t('toast:success.share'));
      } catch (error) {
        console.error('Error sharing media:', error);
        // 如果分享失败，下载媒体文件
        await downloadMedia();
      }
    } else {
      // 如果不支持原生分享，下载媒体文件
      await downloadMedia();
    }
  };

  const downloadMedia = async () => {
    if (mediaType === 'video') {
      // 对于视频，直接使用原始URL
      const link = document.createElement('a');
      link.href = exportImage;
      link.download = 'Komiko.mp4';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success(t('toast:success.download'));
    } else {
      // 对于图片，添加水印
      downloadURI(await addWatermark(exportImage), 'Komiko.jpg');
      toast.success(t('toast:success.download'));
    }
  };

  return (
    <NextUIProvider>
      <Head>
        <title>{t('pageTitle')}</title>
      </Head>
      <Analytics />
      <main className='h-screen caffelabs text-foreground bg-background'>
        <Header autoOpenLogin={false} />
        <div className='flex'>
          <Sidebar />
          <div className='p-4 mx-auto max-w-6xl w-full pt-20 pb-20 md:pb-4 overflow-y-auto lg:pl-[240px]'>
            <div className='flex justify-center mt-1 mb-6 w-full'>
              <h3 className='text-2xl font-bold text-center bg-gradient-to-r from-primary-500 to-purple-600 bg-clip-text text-transparent'>
                {t('shareStory')}
              </h3>
            </div>

            <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 min-h-[calc(100%-8rem)]'>
              {/* 左侧：媒体预览和上传区域 */}
              <div className='flex flex-col'>
                <Card className='flex-1 p-6 min-h-[400px]'>
                  {exportImage && exportImage !== 'data:,' ? (
                    <div className='relative w-full h-full'>
                      <div className='w-full h-full flex items-center justify-center bg-gray-50 rounded-lg overflow-hidden'>
                        {mediaType === 'video' ? (
                          <video
                            src={exportImage}
                            controls
                            autoPlay
                            muted
                            className='max-w-full max-h-full object-contain'>
                            Your browser does not support the video tag.
                          </video>
                        ) : (
                          <img
                            src={exportImage}
                            alt={postTitle || 'Uploaded content'}
                            className='max-w-full max-h-full object-contain'
                          />
                        )}
                      </div>

                      {/* 操作按钮 */}
                      <div className='absolute top-4 right-4 flex gap-2'>
                        {!pendingFile && (
                          <>
                            <Button
                              isIconOnly
                              size='sm'
                              className='bg-white/90 backdrop-blur-sm shadow-lg'
                              onClick={downloadMedia}>
                              <HiOutlineDownload className='w-4 h-4' />
                            </Button>

                            <Button
                              isIconOnly
                              size='sm'
                              className='bg-white/90 backdrop-blur-sm shadow-lg'
                              onClick={handleShare}>
                              <IconShare3 className='w-4 h-4' />
                            </Button>
                          </>
                        )}

                        <Button
                          isIconOnly
                          size='sm'
                          className='bg-white/90 backdrop-blur-sm shadow-lg'
                          onClick={() => clearAllContent()}>
                          <IconX className='w-4 h-4' />
                        </Button>
                      </div>

                      {/* 媒体类型标签 */}
                      <div className='absolute bottom-4 left-4'>
                        <Chip
                          size='sm'
                          variant='flat'
                          color={
                            mediaType === 'video' ? 'secondary' : 'primary'
                          }
                          startContent={
                            mediaType === 'video' ? (
                              <FiVideo className='w-3 h-3' />
                            ) : (
                              <FiImage className='w-3 h-3' />
                            )
                          }>
                          {mediaType === 'video' ? 'Video' : 'Image'}
                        </Chip>
                      </div>
                    </div>
                  ) : (
                    <div
                      className={`w-full h-full border-2 border-dashed rounded-lg transition-all duration-300 cursor-pointer ${
                        dragActive
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
                      }`}
                      onDragEnter={handleDrag}
                      onDragLeave={handleDrag}
                      onDragOver={handleDrag}
                      onDrop={handleDrop}
                      onClick={() => fileInputRef.current?.click()}>
                      <div className='flex flex-col items-center justify-center h-full gap-4 p-8'>
                        <div className='p-4 rounded-full bg-primary-100'>
                          <IconCloudUpload className='w-8 h-8 text-primary-500' />
                        </div>
                        <div className='text-center'>
                          <p className='text-lg font-medium text-gray-900 mb-2'>
                            {dragActive ? t('dropFileHere') : t('uploadMedia')}
                          </p>
                          <p className='text-sm text-gray-500 mb-4'>
                            {t('dragDropOrClick')}
                          </p>
                          <div className='flex gap-2 justify-center'>
                            <Chip size='sm' variant='flat' color='primary'>
                              JPG
                            </Chip>
                            <Chip size='sm' variant='flat' color='primary'>
                              PNG
                            </Chip>
                            <Chip size='sm' variant='flat' color='primary'>
                              WEBP
                            </Chip>
                            <Chip size='sm' variant='flat' color='secondary'>
                              MP4
                            </Chip>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </Card>
              </div>

              {/* 右侧：表单区域 */}
              <div className='flex flex-col'>
                <Card className='flex-1 p-6'>
                  <div className='flex flex-col gap-4'>
                    <div>
                      <label className='text-sm font-medium text-gray-700 mb-2 block'>
                        {t('storyTitle')}
                      </label>
                      <Input
                        placeholder={t('enterStoryTitle')}
                        value={postTitle}
                        onValueChange={setPostTitle}
                        variant='bordered'
                        classNames={{
                          input: 'text-base',
                          inputWrapper: 'h-12',
                        }}
                      />
                      {/* <div className='flex justify-between items-center mt-1'>
                        <p className='text-xs text-gray-500'>
                          {postTitle.length}/40
                        </p>
                        {postTitle.length > 35 && (
                          <p className='text-xs text-warning'>
                            {t('titleLengthWarning')}
                          </p>
                        )}
                      </div> */}
                    </div>

                    <div>
                      <label className='text-sm font-medium text-gray-700 mb-2 block'>
                        {t('description')}
                      </label>
                      <Textarea
                        placeholder={t('addMoreDescription')}
                        value={postContent}
                        onValueChange={setPostContent}
                        minRows={2}
                        maxRows={12}
                        variant='bordered'
                        classNames={{
                          input: 'text-base',
                        }}
                      />
                      <p className='text-xs text-gray-500 mt-1'>
                        {postContent.length} {t('characters')}
                      </p>
                    </div>

                    <Divider className='my-2' />

                    <div className='mb-4'>
                      <TagSelector
                        selectedTags={selectedTags}
                        onTagsChange={setSelectedTags}
                        maxTags={15}
                        placeholder={t('tagPlaceholder')}
                      />
                    </div>

                    {/* 置顶选项 - 只有官方账号可见 */}
                    {isOfficialAccount && (
                      <div className='mb-1'>
                        <Checkbox
                          isSelected={pinPost}
                          onValueChange={setPinPost}
                          color='primary'
                          size='sm'>
                          <span className='text-sm text-red-700'>
                            <span className='flex items-center gap-1'>
                              <RiPushpinLine className='w-4 h-4' />
                              Pin this post to the top of Trending feed
                            </span>
                          </span>
                        </Checkbox>
                      </div>
                    )}

                    {/* 翻译选项 - 只有官方账号可见 */}
                    {isOfficialAccount && (
                      <div className='mb-1'>
                        <Checkbox
                          isSelected={translatePost}
                          onValueChange={setTranslatePost}
                          color='primary'
                          size='sm'>
                          <span className='text-sm text-blue-700'>
                            <span className='flex items-center gap-1'>
                              <RiTranslate2 className='w-4 h-4' />
                              Translate this post to multiple languages
                            </span>
                          </span>
                        </Checkbox>
                      </div>
                    )}

                    <div className='flex flex-col gap-3'>
                      <Button
                        color='primary'
                        variant='solid'
                        size='lg'
                        className='w-full font-medium hover:cursor-pointer'
                        isLoading={loading}
                        disabled={
                          !postTitle ||
                          ((!exportImage || exportImage === 'data:,') &&
                            !pendingFile)
                        }
                        onClick={async () => {
                          // 检查登录状态
                          if (!isAuth) {
                            loginModalState.onOpen?.();
                            return;
                          }

                          if (!postTitle) {
                            toast.error(t('pleaseEnterTitle'));
                            return;
                          }
                          if (
                            (!exportImage || exportImage === 'data:,') &&
                            !pendingFile
                          ) {
                            toast.error(t('pleaseCreateStory'));
                            return;
                          }
                          const postId = await handlePublish(
                            postTitle,
                            postContent,
                            exportImage,
                            postGeneratedImageUrls,
                            setLoading,
                            [], // tags
                            false, // hideMainFeed
                            true, // showToast
                            t,
                            mediaType,
                            pendingFile,
                            selectedTags,
                            pinPost, // pin
                            translatePost, // translate
                          );
                          if (postId) {
                            // 清理atom状态，防止下次进入时仍有数据
                            clearAllContent();

                            router.push(
                              `${window.location.origin}/post/${postId}?showSocial=true`,
                            );
                          }
                        }}>
                        {t('post')}
                      </Button>

                      <div className='p-3 bg-gray-50 rounded-lg'>
                        <p className='text-xs text-gray-600 leading-relaxed'>
                          <span className='font-medium'>{t('pleaseNote')}</span>{' '}
                          <strong>{t('nsfwWarning')}</strong>
                        </p>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type='file'
          accept='image/*,video/*'
          onChange={handleFileInputChange}
          className='hidden'
        />
      </main>
      <LoginModal />
    </NextUIProvider>
  );
};

export default PublishWorkComponent;
