{"title": "Video zu Video KI", "description": "Verwandeln Sie Ihre Videos mit der Kraft der Video-zu-Video-KI. Unsere fortschrittliche Technologie zum Übertragen von Videostilen konvertiert jedes Video in Anime-, Cartoon-, Manga- oder Manhwa-Stile, wobei die ursprüngliche Bewegung und das Timing erhalten bleiben.", "meta": {"title": "Video zu Video KI | Der beste Video-Stiltransfer", "description": "Verwandeln Sie Ihre Videos mit der Kraft der Video-zu-Video-KI. Unsere fortschrittliche Technologie zum Übertragen von Videostilen konvertiert jedes Video in Anime-, Cartoon-, Manga- oder Manhwa-Stile, wobei die ursprüngliche Bewegung und das Timing erhalten bleiben.", "fullDescription": "Revolutionäres Video-zu-Video-K<PERSON><PERSON><PERSON><PERSON>, das gewöhnliche Videos in Anime-Meisterwerke, Cartoon-Animationen, Manga-artige Grafiken und Manhwa-Ästhetik verwandelt. Unsere fortschrittliche Video-Stiltransfer-Technologie bewahrt die Bewegungskonsistenz und wendet gleichzeitig künstlerische Transformationen an, darunter Studio Ghibli-Anime, koreanische Manhwa, japanische Manga, Aquarellanimation und Cartoon-Stile. Perfekt für Content-Ersteller, Animatoren und Künstler, die einen professionellen Video-Stiltransfer suchen.", "keywords": "Video zu Video KI, Video-<PERSON><PERSON><PERSON>sfer, Anime-Video-Generator, Cartoon-Video-Filter, Manga-Stiltransfer, Manhwa-Animation, Video zu Anime-Konverter, Animationstool, Cartoon-Video-Maker, Anime-Stil-Video, Manga-Video-Ersteller, Videostilisierung, animierter Video-Generator, Comic-Video-Filter, Anime-Transformation, Cartoon-Stiltransfer"}, "ui": {"title": "Video zu Video KI", "tooltip": "Verwandeln Sie Ihr Video mit der Video-zu-Video-KI- und Video-Stiltransfer-Technologie", "generatedVideos": "Gestylte Videos", "emptyState": "Ihre gestylten Anime- und Cartoon-Videos werden hier angezeigt", "steps": {"uploadVideo": "1. <PERSON> hochladen", "styleSelection": "Stil auswählen", "generateVideo": "3. <PERSON> generieren"}, "upload": {"dragText": "Zum Hochladen tippen oder Video hierher ziehen", "formatInfo": "Unterstützt MP4, MOV, AVI • Maximal 15 Sekunden • Maximal 50MB", "extractFrameManually": "<PERSON><PERSON> manuell extra<PERSON>n", "bestResultsTip": "<PERSON>ür optimale Ergebnisse verwenden Sie ein Video mit einer einzigen Szene – idealerweise in einer einzigen, ununterbrochenen Aufnahme.", "safariFormatInfo": "Unterstützt MP4, MOV, AVI • Maximal 15 Sekunden • Maximal 50MB", "safariNotice": "Hinweis für Safari-Nutzer", "safariLimitWarning": "Videolänge ist auf 15 Sekunden oder weniger beschränkt. Für längere Videos oder eine benutzerdefinierte Steuerung der Dauer nutzen Si<PERSON> bitte den Chrome-Browser.", "bestResultsTip1": "<PERSON>ür optimale Ergebnisse verwenden Sie ein Video, das in einer durchgängigen Aufnahme gedreht wurde.", "bestResultsTip2": "<PERSON>n ein Schauspieler zu sehen ist, sollte der erste Frame eine frontale Ansicht zeigen. Idealerweise das Video auf einen Hauptdarsteller beschränken und mindestens den gesamten Oberkörper zeigen."}, "duration": {"title": "<PERSON><PERSON>", "description": "Wählen Sie die Dauer für die Videogenerierung. Längere Dauern verbrauchen mehr Zaps.", "originalDuration": "Original: {{duration}}s", "tooLong": "<PERSON><PERSON> lang", "willBeTrimmed": "Wird von {{original}}s auf {{target}}s g<PERSON><PERSON><PERSON>t", "originalLength": "Ursprüngliche Länge", "safariNote": "Safari erkannt: Originale Videolänge wird für beste Kompatibilität verwendet.", "chromeAdvice": "<PERSON>ür erweiterte Kontrollmöglichkeiten über die Dauer verwenden Sie bitte den Chrome-Browser.", "safariUseOriginal": "Safari-Nutzer: Das Video behält seine ursprüngliche Dauer für optimale Kompatibilität."}, "videoMode": {"title": "Generierungsmodus", "human": "Human-Video-Modus", "humanDescription": "Optimiert für menschliche Motive und Porträtvideos", "general": "Allgemeiner Modus", "generalDescription": "Funktioniert mit jedem Motiv- und Szenentyp"}, "videoPrompt": {"title": "Prompt (Optional)", "placeholder": "z.B. Anime-Mädchen tanzt", "description": "Fügen Sie weitere Details hinzu, um den Videogenerierungsprozess zu steuern"}, "framePreview": {"original": "Original", "styled": "Gestylt", "applyingStyle": "Stil wird angewendet...", "awaitingStyle": "Warte auf Stil", "selectStyleBelow": "<PERSON><PERSON><PERSON>en Si<PERSON> unten einen Stil aus", "beforeAfterComparison": "Vorher-Nachher-Vergleich des Stiltransfers", "applyingStyleToFrame": "Wende den gewählten Stil auf den Frame an...", "frameReferenceText": "Dieser Frame dient als Referenz für den Video-Stiltransfer", "styleTooltip": "Dieser gestylte Frame dient als Leitfaden für die gesamte Videotransformation."}, "styleModes": {"templates": "Vorlagen", "prompt": "Prompt", "reference": "<PERSON><PERSON><PERSON><PERSON>"}, "styleTemplates": {"anime": "Anime", "ghibliAnime": "<PERSON><PERSON><PERSON><PERSON>", "koreanManhwa": "Koreanische Manhwa", "cartoon": "Cartoon", "manga": "Manga", "inkWash": "Tuschmalerei", "watercolor": "<PERSON><PERSON><PERSON>", "lineArt": "Strichzeichnung", "lowPoly": "Low Poly", "clay": "Knetanimation", "pixelArt": "Pixel Art", "origami": "Origami-Papier", "lego": "Lego", "vaporwave": "Vaporwave", "rickAndMorty": "<PERSON> and <PERSON><PERSON><PERSON>", "southPark": "South Park", "simpsons": "Simpsons", "naruto": "<PERSON><PERSON><PERSON>", "onePiece": "One Piece", "myLittlePony": "My Little Pony", "comic": "<PERSON><PERSON><PERSON>", "miku": "<PERSON><PERSON>", "barbie": "Barbie", "goku": "<PERSON><PERSON> (Dragon Ball)", "trump": "<PERSON>", "princess": "Prinzessin / Prinz", "kimono": "Kimono / Yukata", "superhero": "Superheld", "magicalGirl": "Magisches Mädchen", "hogwarts": "Hogwarts", "cowboy": "Cowboy", "sailorUniform": "Matrosenuniform", "pixar": "Pixar", "apocalypse": "Apokalypse", "magicalWorld": "Magische Welt", "dreamland": "Traumland", "cyberpunk": "Cyberpunk", "kpopIdol": "K-Pop-Idol", "cloud": "<PERSON><PERSON><PERSON>", "mars": "Mars", "outerSpace": "Weltraum", "sailorMoon": "<PERSON>", "pencilSketch": "Bleistiftskizze", "retroGame": "Retro-Spiel", "mobileGame": "Mobilspiel", "psGame": "PS-Spiel", "underwater": "Unterwasserwelt", "snow": "Schneelandschaft", "toyBricks": "<PERSON><PERSON><PERSON><PERSON>", "skeleton": "Skelett", "fire": "<PERSON><PERSON>", "muscle": "Muskeln", "metal": "Metall", "crystal": "<PERSON><PERSON><PERSON>", "westernAnimation": "Westliche Animation", "vanGogh": "<PERSON>", "oilPainting": "Ö<PERSON>mä<PERSON>"}, "prompt": {"placeholder": "Beschreiben Sie Ihre gewünschte Stiltransformation...", "example": "Beispiel: \"<PERSON><PERSON><PERSON><PERSON> den Mann durch Naruto\", \"<PERSON><PERSON><PERSON><PERSON> dies in einen klassischen 90er-Jahre-Anime-Stil\", \"<PERSON><PERSON><PERSON> das Mädchen in ein Blumenkleid\""}, "reference": {"uploadText": "Laden Sie Ihren vorgestylten Frame als Referenz hoch", "formatInfo": "Unterstützt JPG, PNG, JPEG, WEBP • Max. 10MB", "compositionWarning": "<PERSON><PERSON><PERSON>, dass das Referenzbild in der Komposition genau mit dem ersten Frame des Originalvideos übereinstimmt."}, "buttons": {"applying": "Wird angewendet...", "useNewReference": "Neue Referenz verwenden", "applyNewStyle": "Neuen Stil anwenden", "useReference": "Refer<PERSON>z verwenden", "applyStyle": "<PERSON><PERSON> <PERSON><PERSON>", "generateVideo": "Video generieren", "generatingVideo": "Video wird generiert...", "generateMore": "Weitere Videos generieren", "createAnother": "Weiteres Video erstellen"}, "separators": {"readyToGenerate": "Bereit zum Generieren des Videos"}, "styleCategories": {"tStyleTransfer": "Stilübertragung", "changeMaterial": "Material ändern", "changeEnvironment": "Umgebung ändern", "cosplay": "Kostümspiel"}}, "whatIs": {"title": "Was ist Video zu Video KI?", "description": "Video zu Video KI verwandelt gewöhnliche Videos mit fortschrittlicher Video-Stiltransfer-Technologie in Anime-, Cartoon-, Manga- und Manhwa-Animationen. Unser zweistufiger Prozess wendet zuerst den von Ihnen gewählten Stil auf einen Referenzframe an und verwendet diesen gestylten Frame dann, um Ihr gesamtes Video zu transformieren, wobei die ursprüngliche Bewegung und das Timing erhalten bleiben. Wählen Sie aus über 20 Stilen, darunter Studio Ghibli-Anime, koreanische Manhwa, japanische Manga und beliebte Cartoon-Ästhetiken mit unserem Video-Stiltransfer."}, "examples": {"title": "Video zu Video KI Beispiele", "description": "<PERSON><PERSON>, wie unsere Video zu Video KI Videos in beeindruckende Anime-, Cartoon-, Manga- und Manhwa-Stile verwandelt und gleichzeitig perfekte Bewegungskonsistenz durch fortschrittlichen Video-Stiltransfer beibehält.", "description1": "Charakteränderung | Aufgabe: <PERSON><PERSON><PERSON><PERSON> das Mädchen in Donald <PERSON>", "description2": "Charakteränderung | Aufgabe: <PERSON><PERSON><PERSON><PERSON> das Mädchen in Sailor Moon", "description3": "Stiltransfer | Echte Tanzaufnahme im Anime-Stil", "originalVideo": "Originalvideo", "animeVideo": "Video im Anime-Stil", "watercolorVideo": "<PERSON><PERSON><PERSON>", "style": "Angewendeter Stil", "prompt": "Verwendeter Stil-Prompt", "description5": "Stiltransfer | Echte Tanzaufnahme im Comic-Stil", "description6": "Szenentransformation | Aufgabe: Verwandle das Bild so, dass der Schauspieler in einer Cyberpunk-Umgebung geht – eine extrem Sci-Fi-lastige Szene", "animeStyle": "Anime-Stil", "comicStyle": "Comic-Stil", "promptUsed": "Verwendetes Stil-Prompt", "animeTransformation": "Anime-Verwandlung", "description7": "Verwandlung eines echten Hundes in einen Anime-Stil, bei der ein realistisches Haustier in einen animierten Charakter umgewandelt wird.", "description8": "<PERSON>ände beim Stricken im Anime-Stil, die eine detaillierte Bewegungserhaltung bei handwerklichen Tätigkeiten demonstrieren."}, "howTo": {"title": "So verwenden Sie Video zu Video KI"}, "steps": {"step1": {"title": "Laden Sie Ihr Video hoch", "content": "Laden Sie eine Videodatei (MP4, MOV, AVI) mit einer Dauer von bis zu 15 Sekunden und einer Größe von maximal 50MB für die Video-zu-Video-KI-Verarbeitung hoch. Videos, die länger als 15 Sekunden sind, werden für einen optimalen Video Style Transfer automatisch gekürzt."}, "step2": {"title": "Video-Stiltransfer-Referenz", "content": "Wir extrahieren den ersten Frame und wenden Ihren gewählten Anime-, Cartoon- oder Manga-Stil an, um eine Referenz für eine konsistente Video-Stiltransfer-Transformation zu erstellen."}, "step3": {"title": "Wählen Sie einen künstlerischen Stil", "content": "Wählen Sie aus über 20 voreingestellten Stilen wie Studio Ghibli-Anime, koreanische Manhwa, japanische Manga oder erstellen Sie benutzerdefinierte Stile mit Text-Prompts und Referenzbildern für den Video-Stiltransfer."}, "step4": {"title": "Video zu Video KI generieren", "content": "Unsere Video zu Video KI transformiert Ihr komplettes Video mithilfe des gestylten Referenzframes und bewahrt dabei alle Originalbewegungen, -ausdrücke und -zeitabläufe durch fortschrittlichen Video-Stiltransfer."}}, "benefits": {"title": "Warum Video zu Video KI verwenden", "description": "Unsere Video zu Video KI bietet den fortschrittlichsten Video-Stiltransfer mit Bewegungserhaltung, umfangreichen Stiloptionen und transparenter Preisgestaltung."}, "features": {"feature1": {"title": "Perfekte Bewegungserhaltung", "content": "Video zu Video KI behält jedes Detail der Originalbewegung, der Gesichtsausdrücke und des Timings bei, wä<PERSON>d Anime-, Cartoon- oder Manga-Stile mit frameperfekter Video-Stiltransfer-Konsistenz angewendet werden."}, "feature2": {"title": "Über 20 Video-Stiltransfer-Optionen", "content": "Wählen Sie aus Studio Ghibli-Anime, koreanischer Manhwa, japanischem Manga, Disney-Cartoon, Naruto-Stil und mehr. Erstellen Sie benutzerdefinierte Video-Stiltransfers mit Text-Prompts oder Referenzbildern mit Video zu Video KI."}, "feature3": {"title": "Ausgabe in Profi-Qualität", "content": "Generieren Sie hochauflösende Anime- und Cartoon-Videos mit konsistenter Video-Stiltransfer-Anwendung, reibungslosen Übergängen und ohne Flimmern oder Artefakte mit unserer Video zu Video KI."}, "feature4": {"title": "Intelligentes Kostensystem", "content": "Transparente Preisgestaltung mit separaten Gebühren für Video-Stiltransfer und Videogenerierung. Experimentieren Sie mit verschiedenen Stilen ohne zusätzliche Videokosten mit Video zu Video KI."}, "feature5": {"title": "Einfacher zweistufiger Prozess", "content": "Einfacher Video-zu-Video-KI-Workflow: Video hochladen, Video-Stiltransfer auf Referenzframe anwenden, vollständiges Video generieren. Keine technischen Vorkenntnisse erforderlich mit Echtzeit-Fortschrittsverfolgung."}, "feature6": {"title": "Automatische Optimierung", "content": "Intelligente Video zu Video KI-Verarbeitung mit automatischer Kürzung, Formatunterstützung (MP4, MOV, AVI) und dauerbasierter Kostenberechnung für optimalen Video-Stiltransfer."}}, "faq": {"title": "Video zu Video KI FAQ", "description": "Häufige Fragen zu unserem Video-zu-Video-KI- und Video-Stiltransfer-Tool, <PERSON><PERSON><PERSON>, <PERSON>sten und Best Practices.", "q1": "Wie funktioniert Video zu Video KI?", "a1": "Unsere Video zu Video KI verwendet einen zweistufigen Video-Stiltransfer-Prozess: 1) Extrahieren Sie einen Referenzframe und wenden Sie Ihren gewählten Anime-/Cartoon-Stil an, 2) Transformieren Sie das gesamte Video mithilfe dieses gestylten Frames, wobei die ursprüngliche Bewegung und das Timing erhalten bleiben. Dies gewährleistet eine konsistente Video-Stiltransfer-Anwendung über alle Frames hinweg.", "q2": "Was sind die Videoformatanforderungen für Video zu Video KI?", "a2": "Wir unterstützen die Formate MP4, MOV und AVI mit einer maximalen Dateigröße von 50MB für die Video-zu-Video-KI-Verarbeitung. Videos sind auf 15 Sekunden begrenzt und werden bei Überschreitung dieser Länge automatisch gekürzt, um eine optimale Verarbeitung und Kosteneffizienz beim Video Style Transfer zu gewährleisten.", "q3": "Wie lange dauert der Video-Stiltransfer?", "a3": "Die gesamte Video-zu-Video-KI-Verarbeitung dauert 5-10 Minuten: Video-Stiltransfer (1-3 Minuten) und Videogenerierung (3-7 Minuten). Sie können den Video-Stiltransfer-Fortschritt in Echtzeit überwachen.", "q4": "Was kostet Video zu Video KI?", "a4": "Separate Gebühren für Video-Stiltransfer und Videogenerierung basierend auf der Dauer. Die Video zu Video KI-Kosten werden vor der Verarbeitung in Echtzeit angezeigt, und Guthaben werden erst nach erfolgreichem Abschluss des Video-Stiltransfers abgezogen.", "q5": "Kann ich benutzerdefinierte Video-Stiltransfer-Stile erstellen?", "a5": "Ja! Wählen Sie aus über 20 voreingestellten Vorlagen (Studio Ghibli, koreanische Manhwa, japanische Manga usw.), schreiben Sie benutzerdefinierte Text-Prompts oder laden Sie Referenzbilder für einzigartige Video-Stiltransfer-Transformationen mit unserer Video zu Video KI hoch.", "q6": "Was macht gute Eingangsvideos für Video zu Video KI aus?", "a6": "Beste Video-Stiltransfer-Ergebnisse erzielen Sie mit klaren Mo<PERSON>n, guter Beleuchtung, stabiler Bewegung und gut definierten Merkmalen. Vermeiden Sie schnelle Bewegungen, dunkles oder unscharfes Filmmaterial für eine optimale Video-zu-Video-KI-Verarbeitung. Videos unter 5 Sekunden mit Personen oder klaren Objekten funktionieren am besten für den Video-Stiltransfer."}}