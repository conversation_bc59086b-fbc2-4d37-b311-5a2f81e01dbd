{"title": "Vidéo en Vidéo IA", "description": "Transformez vos vidéos avec la puissance de l'IA Vidéo en Vidéo. Notre technologie de transfert de style vidéo avancée convertit vos vidéos en styles anime, cartoon, manga et manhwa, tout en préservant les mouvements et le rythme originaux.", "meta": {"title": "Vidéo en Vidéo IA | Le meilleur outil de transfert de style vidéo", "description": "Transformez vos vidéos avec la puissance de l'IA Vidéo en Vidéo. Notre technologie de transfert de style vidéo avancée convertit vos vidéos en styles anime, cartoon, manga et manhwa, tout en préservant les mouvements et le rythme originaux.", "fullDescription": "Découvrez l'outil révolutionnaire Vidéo en Vidéo IA qui métamorphose vos vidéos ordinaires en chefs-d'œuvre anime, animations de dessins animés, créations de style manga et esthétiques manhwa. Notre technologie avancée de transfert de style vidéo assure une cohérence des mouvements tout en appliquant des transformations artistiques, incluant les styles anime Studio Ghibli, manhwa coréen, manga japonais, animation à l'aquarelle et dessins animés. Parfait pour les créateurs de contenu, les animateurs et les artistes recherchant un transfert de style vidéo de qualité professionnelle.", "keywords": "Vidéo en Vidéo IA, Transfert de style vidéo, générateur de vidéos anime, filtre vidéo cartoon, transfert de style manga, animation manhwa, convertisseur vidéo en anime, outil d'animation, créateur de vidéos cartoon, vidéo de style anime, créateur de vidéos manga, stylisation vidéo, générateur de vidéos animées, filtre vidéo comique, transformation anime, transfert de style cartoon"}, "ui": {"title": "Vidéo en Vidéo IA", "tooltip": "Métamorphosez votre vidéo grâce à l'IA Vidéo en Vidéo et au Transfert de style vidéo", "generatedVideos": "Vidéos stylisées générées", "emptyState": "Vos vidéos stylisées anime et cartoon apparaîtront ici", "steps": {"uploadVideo": "1. Télécharger la vidéo", "styleSelection": "Sélection du style", "generateVideo": "3. <PERSON><PERSON><PERSON><PERSON> la vidéo"}, "upload": {"dragText": "Cliquez pour télécharger ou glissez-déposez votre vidéo ici", "formatInfo": "Formats pris en charge : MP4, MOV, AVI • Durée maximale : 15 secondes • <PERSON>lle maximale : 50 Mo", "extractFrameManually": "Extraire manuellement la première image", "bestResultsTip": "Pour des résultats optimaux, utilisez une vidéo d'une seule scène, idéalement filmée en une seule prise continue.", "safariFormatInfo": "Formats pris en charge : MP4, MOV, AVI • Durée maximale : 15 secondes • <PERSON>lle maximale : 50 Mo", "safariNotice": "Avis pour les utilisateurs de Safari", "safariLimitWarning": "La durée des vidéos est limitée à 15 secondes ou moins. Pour des vidéos plus longues ou une durée personnalisée, veuillez utiliser le navigateur Chrome.", "bestResultsTip1": "Pour obtenir de meilleurs résultats, utilisez une vidéo enregistrée en une seule prise continue.", "bestResultsTip2": "Si elle comporte un acteur humain, la première séquence doit montrer une vue de face. Idé<PERSON><PERSON>, limitez la vidéo à un acteur principal et incluez au moins le haut du corps en entier."}, "duration": {"title": "<PERSON><PERSON><PERSON>", "description": "Sélectionnez la durée de la vidéo générée. Les durées plus longues consomment plus de zaps.", "originalDuration": "Originale : {{duration}}s", "tooLong": "Trop long", "willBeTrimmed": "Sera réduite de {{original}}s à {{target}}s", "originalLength": "Durée originale", "safariNote": "Safari détecté : utilisation de la durée originale de la vidéo pour une meilleure compatibilité", "chromeAdvice": "Pour un contrôle de durée personnalisé, veuillez utiliser le navigateur Chrome", "safariUseOriginal": "Utilisateurs de Safari : la vidéo utilisera sa durée originale pour une compatibilité optimale."}, "videoMode": {"title": "Mode de génération", "human": "Mode vidéo humaine", "humanDescription": "Optimisé pour les sujets humains et les vidéos de portrait", "general": "Mode général", "generalDescription": "Fonctionne avec n'importe quel sujet et type de scène"}, "videoPrompt": {"title": "Prompt (Facultatif)", "placeholder": "Ex. : fille anime qui danse", "description": "Ajoutez des détails supplémentaires pour guider le processus de génération de la vidéo"}, "framePreview": {"original": "Originale", "styled": "<PERSON><PERSON><PERSON><PERSON>", "applyingStyle": "Application du style en cours...", "awaitingStyle": "En attente du style", "selectStyleBelow": "Sélectionnez un style ci-dessous", "beforeAfterComparison": "Comparaison du transfert de style avant/après", "applyingStyleToFrame": "Application du style sélectionné à l'image...", "frameReferenceText": "Cette image servira de référence pour le transfert de style vidéo", "styleTooltip": "Cette image stylisée guidera la transformation de l'ensemble de la vidéo."}, "styleModes": {"templates": "<PERSON><PERSON><PERSON><PERSON>", "prompt": "Prompt", "reference": "Référence"}, "styleTemplates": {"anime": "Anime", "ghibliAnime": "<PERSON><PERSON>", "koreanManhwa": "Manhwa coréen", "cartoon": "Cartoon", "manga": "Manga", "inkWash": "Lavis à l'encre", "watercolor": "<PERSON><PERSON><PERSON><PERSON>", "lineArt": "Line Art", "lowPoly": "Low Poly", "clay": "Claymation", "pixelArt": "Pixel Art", "origami": "<PERSON><PERSON>r or<PERSON>", "lego": "Lego", "vaporwave": "Vaporwave", "rickAndMorty": "<PERSON> et Morty", "southPark": "South Park", "simpsons": "Simpsons", "naruto": "<PERSON><PERSON><PERSON>", "onePiece": "One Piece", "myLittlePony": "My Little Pony", "comic": "BD", "miku": "<PERSON><PERSON>", "barbie": "Barbie", "goku": "<PERSON><PERSON> (Dragon Ball)", "trump": "<PERSON>", "princess": "Princesse / Prince", "kimono": "Kimono / Yukata", "superhero": "Super-héros", "magicalGirl": "Fille magique", "hogwarts": "<PERSON><PERSON><PERSON>", "cowboy": "Cowboy", "sailorUniform": "Uniforme de marin", "pixar": "Pixar", "apocalypse": "Apocalypse", "magicalWorld": "Monde magique", "dreamland": "Pays des rêves", "cyberpunk": "Cyberpunk", "kpopIdol": "Idole Kpop", "cloud": "Nuage", "mars": "Mars", "outerSpace": "Espace", "sailorMoon": "<PERSON>", "pencilSketch": "Esquis<PERSON> au crayon", "retroGame": "<PERSON><PERSON><PERSON>", "mobileGame": "Jeu mobile", "psGame": "Jeu PlayStation", "underwater": "Sous-marin", "snow": "<PERSON><PERSON><PERSON><PERSON>", "toyBricks": "Blocs de jouets", "skeleton": "Squelettique", "fire": "<PERSON><PERSON><PERSON>", "muscle": "Musclé", "metal": "Métallique", "crystal": "<PERSON><PERSON><PERSON><PERSON>", "westernAnimation": "Dessin animé occidental", "vanGogh": "<PERSON>", "oilPainting": "Peinture à l'huile"}, "prompt": {"placeholder": "Décrivez la transformation de style souhaitée...", "example": "Exemple : « Remplacez l'homme par Naruto », « Transformez ceci en un style anime classique des années 90 », « Habillez la fille d'une robe à fleurs »"}, "reference": {"uploadText": "Téléchargez votre image pré-stylisée comme référence", "formatInfo": "Formats supportés : JPG, PNG, JPEG, WEBP • Max. 10 Mo", "compositionWarning": "Assurez-vous que l'image de référence correspond exactement à la composition de la première image de la vidéo originale."}, "buttons": {"applying": "Application en cours...", "useNewReference": "Utiliser une nouvelle référence", "applyNewStyle": "Appliquer un nouveau style", "useReference": "Utiliser la référence", "applyStyle": "Appliquer le style", "generateVideo": "Générer la vidéo", "generatingVideo": "Génération de la vidéo en cours...", "generateMore": "Générer d'autres vidéos", "createAnother": "<PERSON><PERSON>er une autre vidéo"}, "separators": {"readyToGenerate": "<PERSON><PERSON><PERSON>t à générer la vidéo"}, "styleCategories": {"tStyleTransfer": "Transfert de Style", "changeMaterial": "Modifier le Matériau", "changeEnvironment": "Modifier l'Environnement", "cosplay": "Costume"}}, "whatIs": {"title": "Qu'est-ce que Vidéo en Vidéo IA ?", "description": "Vidéo en Vidéo IA transforme les vidéos ordinaires en animations de style anime, cartoon, manga et manhwa grâce à une technologie avancée de transfert de style vidéo. Notre processus en deux étapes applique d'abord le style choisi à une image de référence, puis utilise cette image stylisée pour transformer l'ensemble de votre vidéo, tout en préservant le mouvement et le rythme originaux. Choisissez parmi plus de 20 styles, incluant l'anime Studio Ghibli, le manhwa coréen, le manga japonais et les esthétiques de dessins animés populaires grâce à notre transfert de style vidéo."}, "examples": {"title": "Exemples de Vidéo en Vidéo IA", "description": "Découvrez comment notre Vidéo en Vidéo IA transforme les vidéos en de superbes styles anime, cartoon, manga et manhwa, tout en conservant une cohérence parfaite du mouvement grâce au transfert de style vidéo avancé.", "description1": "Changement de Personnage | Demande : <PERSON><PERSON><PERSON><PERSON> la fille par <PERSON>", "description2": "Changement de Personnage | Demande : <PERSON><PERSON><PERSON><PERSON> la fille par <PERSON> Moon", "description3": "Transfert de Style | Transformez une vidéo de danse réelle en style anime", "originalVideo": "Vidéo d'Origine", "animeVideo": "Vidéo de style anime", "watercolorVideo": "Style d'animation à l'aquarelle", "style": "Style appliqué", "prompt": "Prompt de style utilisé", "description5": "Transfert de Style | Transformez une vidéo de danse réelle en style bande dessinée", "description6": "Transformation de Scène | Demande : Modifiez l'image pour que l'acteur marche dans un décor cyberpunk — une scène très science-fiction", "animeStyle": "Style Anime", "comicStyle": "Style Bande Dessinée", "promptUsed": "Prompt de Style Utilisé", "animeTransformation": "Transformation en style anime", "description7": "Conversion d'un chien réel en style anime, montrant un animal de compagnie réaliste transformé en personnage animé", "description8": "Des mains tricotant changées en style anime, illustrant la conservation des mouvements détaillés dans les activités artisanales"}, "howTo": {"title": "Comment utiliser Vidéo en Vidéo IA"}, "steps": {"step1": {"title": "Téléchargez votre vidéo", "content": "Téléchargez une vidéo (MP4, MOV, AVI) d'une durée maximale de 15 secondes et de 50 Mo pour le traitement vidéo par IA. Les vidéos de plus de 15 secondes seront automatiquement raccourcies pour un transfert de style optimal."}, "step2": {"title": "Référence de transfert de style vidéo", "content": "Nous extrayons la première image et appliquons le style anime, cartoon ou manga choisi pour créer un guide de référence pour une transformation cohérente du transfert de style vidéo."}, "step3": {"title": "Choisissez le style artistique", "content": "Choisissez parmi plus de 20 styles prédéfinis comme l'anime Studio Ghibli, le manhwa coréen, le manga japonais, ou créez des styles personnalisés avec des prompts de texte et des images de référence pour le transfert de style vidéo."}, "step4": {"title": "Générer Vidéo en Vidéo IA", "content": "Notre Vidéo en Vidéo IA transforme votre vidéo complète en utilisant l'image de référence stylisée, tout en préservant tous les mouvements, expressions et le rythme originaux grâce au transfert de style vidéo avancé."}}, "benefits": {"title": "Pourquoi utiliser Vidéo en Vidéo IA", "description": "Notre Vidéo en Vidéo IA offre le transfert de style vidéo le plus avancé avec la préservation du mouvement, de nombreuses options de style et une tarification transparente."}, "features": {"feature1": {"title": "Préservation parfaite du mouvement", "content": "Vidéo en Vidéo IA conserve tous les détails du mouvement d'origine, des expressions faciales et du rythme tout en appliquant des styles anime, cartoon ou manga avec une cohérence parfaite du transfert de style vidéo."}, "feature2": {"title": "Plus de 20 options de transfert de style vidéo", "content": "Choisissez parmi l'anime Studio Ghibli, le manhwa coréen, le manga japonais, le cartoon Disney, le style Naruto, et plus encore. Créez un transfert de style vidéo personnalisé avec des prompts de texte ou des images de référence en utilisant Vidéo en Vidéo IA."}, "feature3": {"title": "Sortie de qualité professionnelle", "content": "Générez des vidéos anime et cartoon haute définition avec une application cohérente du transfert de style vidéo, des transitions fluides et aucune scintillement ou artefact avec notre Vidéo en Vidéo IA."}, "feature4": {"title": "Système de coût intelligent", "content": "Tarification transparente avec des frais distincts pour le transfert de style vidéo et la génération de vidéo. Expérimentez avec différents styles sans frais vidéo supplémentaires en utilisant Vidéo en Vidéo IA."}, "feature5": {"title": "Processus simple en deux étapes", "content": "Workflow Vidéo en Vidéo IA simple : téléchargez la vidéo, appliquez le transfert de style vidéo à l'image de référence, g<PERSON><PERSON>rez la vidéo complète. Aucune expertise technique requise, avec un suivi de la progression en temps réel."}, "feature6": {"title": "Optimisation automatique", "content": "Traitement Vidéo en Vidéo IA intelligent avec réduction automatique, prise en charge des formats (MP4, MOV, AVI) et calcul des coûts en fonction de la durée pour un transfert de style vidéo optimal."}}, "faq": {"title": "FAQ Vidéo en Vidéo IA", "description": "Questions fréquentes sur notre outil, processus, coûts et meilleures pratiques Vidéo en Vidéo IA et transfert de style vidéo.", "q1": "Comment fonctionne Vidéo en Vidéo IA ?", "a1": "Notre Vidéo en Vidéo IA utilise un processus de transfert de style vidéo en deux étapes : 1) Extrayez une image de référence et appliquez le style anime/cartoon choisi, 2) Transformez toute la vidéo en utilisant cette image stylisée, tout en préservant le mouvement et le rythme originaux. Cela garantit une application cohérente du transfert de style vidéo sur toutes les images.", "q2": "Quelles sont les exigences de format vidéo pour Vidéo en Vidéo IA ?", "a2": "Nous prenons en charge les formats MP4, MOV et AVI avec une taille maximale de 50 Mo pour le traitement vidéo par IA. Les vidéos sont limitées à 15 secondes et seront automatiquement raccourcies pour assurer un transfert de style optimal et une gestion efficace des coûts.", "q3": "Combien de temps dure le transfert de style vidéo ?", "a3": "Le traitement total Vidéo en Vidéo IA prend de 5 à 10 minutes : transfert de style vidéo (1 à 3 minutes) et génération de vidéo (3 à 7 minutes). Vous pouvez surveiller la progression du transfert de style vidéo en temps réel.", "q4": "Quels sont les coûts pour Vidéo en Vidéo IA ?", "a4": "Frais distincts pour le transfert de style vidéo et la génération de vidéo en fonction de la durée. Les coûts Vidéo en Vidéo IA sont affichés en temps réel avant le traitement, et les crédits ne sont déduits qu'après la réussite du transfert de style vidéo.", "q5": "<PERSON><PERSON><PERSON>-je créer des styles de transfert de style vidéo personnalisés ?", "a5": "Oui ! Choisissez parmi plus de 20 modèles prédéfinis (Studio Ghibli, manhwa coréen, manga japonais, etc.), écrivez des prompts de texte personnalisés ou téléchargez des images de référence pour des transformations uniques de transfert de style vidéo avec notre Vidéo en Vidéo IA.", "q6": "Quelles sont les bonnes vidéos d'entrée pour Vidéo en Vidéo IA ?", "a6": "Meilleurs résultats de transfert de style vidéo avec des sujets clairs, un bon éclairage, un mouvement stable et des caractéristiques bien définies. Évitez les mouvements rapides, les séquences sombres ou floues pour un traitement Vidéo en Vidéo IA optimal. Les vidéos de moins de 5 secondes avec des personnes ou des objets clairs fonctionnent mieux pour le transfert de style vidéo."}}