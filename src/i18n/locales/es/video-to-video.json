{"title": "Video to Video AI", "description": "Transforma tus videos con Video to Video AI. Nuestra avanzada tecnología de Transferencia de Estilo de Video convierte cualquier video en estilos de anime, dibujos animados, manga y manhwa, conservando el movimiento y el ritmo originales.", "meta": {"title": "Video to Video AI | La Mejor Transformación de Video con IA", "description": "Transforma tus videos con Video to Video AI. Nuestra avanzada tecnología de Transferencia de Estilo de Video convierte cualquier video en estilos de anime, dibujos animados, manga y manhwa, conservando el movimiento y el ritmo originales.", "fullDescription": "Herramienta revolucionaria que transforma videos comunes en obras maestras de anime, animaciones, manga y manhwa. Nuestra avanzada tecnología de Transferencia de Estilo de Video preserva la consistencia del movimiento al aplicar transformaciones artísticas que incluyen anime de Studio Ghibli, manhwa coreano, manga japonés, animación de acuarela y estilos de dibujos animados. Ideal para creadores de contenido, animadores y artistas que buscan una Transferencia de Estilo de Video de calidad profesional.", "keywords": "Video to Video AI, Transferencia de Estilo de Video, generador de videos de anime, filtro de videos de dibujos animados, transferencia de estilo manga, animación de manhwa, convertidor de video a anime, herramienta de animación, creador de videos de dibujos animados, video de estilo anime, creador de videos de manga, estilización de video, generador de videos animados, filtro de videos cómicos, transformación de anime, transferencia de estilo de dibujos animados"}, "ui": {"title": "Video to Video AI", "tooltip": "Transforma tu video con Video to Video AI y su tecnología de Transferencia de Estilo de Video", "generatedVideos": "Videos Estilizados Generados", "emptyState": "Aquí aparecerán tus videos con estilo anime o de dibujos animados", "steps": {"uploadVideo": "1. Subir Video", "styleSelection": "Selección de Estilo", "generateVideo": "3. <PERSON><PERSON> Video"}, "upload": {"dragText": "Haz clic para subir o arrastra tu video aquí", "formatInfo": "Formatos admitidos: MP4, MOV, AVI • Duración máxima: 15 segundos • Tamaño máximo: 50MB", "extractFrameManually": "Extraer el Primer Fotograma Manualmente", "bestResultsTip": "Para obtener los mejores resultados, usa un video de una sola escena, idealmente filmado en una toma continua.", "safariFormatInfo": "Formatos admitidos: MP4, MOV, AVI • Duración máxima: 15 segundos • Tamaño máximo: 50MB", "safariNotice": "Nota para usuarios de Safari", "safariLimitWarning": "La duración del video está limitada a 15 segundos o menos. Para videos más largos o control de duración personalizado, por favor usa el navegador Chrome.", "bestResultsTip1": "Para obtener los mejores resultados, utilice un video grabado en una sola toma continua.", "bestResultsTip2": "Si presenta a un actor humano, el primer fotograma debe mostrar una vista frontal. Idealmente, limite el video a un actor principal e incluya al menos la parte superior completa del cuerpo."}, "duration": {"title": "Duración", "description": "Selecciona la duración del video a generar. Las duraciones más largas consumen más zaps.", "originalDuration": "Original: {{duration}}s", "tooLong": "<PERSON><PERSON><PERSON><PERSON>", "willBeTrimmed": "Se recortará de {{original}}s a {{target}}s", "originalLength": "Duración Original", "safariNote": "Safari detectado: Se utilizará la duración original del video para asegurar la mejor compatibilidad", "chromeAdvice": "Para ajustes personalizados de duración, por favor utilice el navegador Chrome", "safariUseOriginal": "Usuarios de Safari: El video utilizará su duración original para garantizar una compatibilidad óptima"}, "videoMode": {"title": "Modo de Generación", "human": "Modo Video Humano", "humanDescription": "Optimizado para sujetos humanos y videos de retrato", "general": "Modo General", "generalDescription": "Funciona con cualquier sujeto y tipo de escena"}, "videoPrompt": {"title": "Prompt (Opcional)", "placeholder": "<PERSON>j., chica <PERSON> bailando", "description": "Añade detalles adicionales para guiar el proceso de generación de video"}, "framePreview": {"original": "Original", "styled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applyingStyle": "Aplicando Estilo...", "awaitingStyle": "<PERSON><PERSON><PERSON><PERSON>", "selectStyleBelow": "Selecciona un estilo abajo", "beforeAfterComparison": "Comparación de la transferencia de estilo antes y después", "applyingStyleToFrame": "Aplicando el estilo seleccionado al fotograma...", "frameReferenceText": "Este fotograma se usará como referencia para la Transferencia de Estilo de Video", "styleTooltip": "Este fotograma estilizado guiará la transformación del video."}, "styleModes": {"templates": "Plantillas", "prompt": "Prompt", "reference": "Referencia"}, "styleTemplates": {"anime": "Anime", "ghibliAnime": "<PERSON><PERSON>", "koreanManhwa": "<PERSON><PERSON>", "cartoon": "<PERSON><PERSON><PERSON>", "manga": "Manga", "inkWash": "<PERSON><PERSON><PERSON>", "watercolor": "<PERSON><PERSON><PERSON><PERSON>", "lineArt": "Arte Lineal", "lowPoly": "Low Poly", "clay": "Animación de Arcilla", "pixelArt": "Pixel Art", "origami": "Papel Origami", "lego": "Lego", "vaporwave": "Vaporwave", "rickAndMorty": "<PERSON>", "southPark": "South Park", "simpsons": "Simpsons", "naruto": "<PERSON><PERSON><PERSON>", "onePiece": "One Piece", "myLittlePony": "My Little Pony", "comic": "Cómico", "miku": "<PERSON><PERSON>", "barbie": "Barbie", "goku": "<PERSON><PERSON> (Dragon Ball)", "trump": "<PERSON>", "princess": "Princesa / Príncipe", "kimono": "Kimono / Yukata", "superhero": "Superhéroe", "magicalGirl": "Chica Mágica", "hogwarts": "Hogwarts", "cowboy": "<PERSON><PERSON><PERSON>", "sailorUniform": "Uniforme de Marinero", "pixar": "Pixar", "apocalypse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "magicalWorld": "Mundo <PERSON>", "dreamland": "País de los Sueños", "cyberpunk": "Ciberpunk", "kpopIdol": "Í<PERSON>lo del Kpop", "cloud": "Nube", "mars": "<PERSON><PERSON>", "outerSpace": "Espacio Exterior", "sailorMoon": "<PERSON>", "pencilSketch": "Boceto a lápiz", "retroGame": "<PERSON><PERSON>", "mobileGame": "Juego para Móvil", "psGame": "<PERSON><PERSON>", "underwater": "Mundo <PERSON>", "snow": "<PERSON><PERSON><PERSON>", "toyBricks": "Ladrillos de Juguete", "skeleton": "Esqueleto Humano", "fire": "Llama", "muscle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metal": "Met<PERSON>lic<PERSON>", "crystal": "<PERSON><PERSON><PERSON><PERSON>", "westernAnimation": "Animación Americana", "vanGogh": "<PERSON><PERSON><PERSON>", "oilPainting": "<PERSON><PERSON><PERSON>"}, "prompt": {"placeholder": "Describe la transformación de estilo que deseas...", "example": "Ejemplo: \"<PERSON>emplaza al hombre con Naruto\", \"Convierte esto en un estilo anime clásico de los 90\", \"Viste a la chica con un vestido floral\""}, "reference": {"uploadText": "Sube tu fotograma pre-estilizado como referencia", "formatInfo": "Admite JPG, PNG, JPEG, WEBP • Máx. 10MB", "compositionWarning": "Asegúrate de que la imagen de referencia coincida con la composición exacta del primer fotograma del video original."}, "buttons": {"applying": "Aplicando...", "useNewReference": "Usar Nueva Referencia", "applyNewStyle": "Aplicar Nuevo Estilo", "useReference": "Usar Referencia", "applyStyle": "<PERSON>p<PERSON><PERSON>", "generateVideo": "Generar Video", "generatingVideo": "Generando Video...", "generateMore": "<PERSON>rar <PERSON>s", "createAnother": "<PERSON><PERSON><PERSON>"}, "separators": {"readyToGenerate": "Listo para generar video"}, "styleCategories": {"tStyleTransfer": "Transferir estilo", "changeMaterial": "Modificar material", "changeEnvironment": "Modificar entorno", "cosplay": "<PERSON><PERSON><PERSON><PERSON>"}}, "whatIs": {"title": "¿Qué es Video to Video AI?", "description": "Video to Video AI transforma videos comunes en animaciones de estilo anime, dibujos animados, manga y manhwa utilizando tecnología avanzada de Transferencia de Estilo de Video. Nuestro proceso de dos pasos aplica primero el estilo elegido a un fotograma de referencia y luego utiliza este fotograma estilizado para transformar todo el video, conservando el movimiento y el ritmo originales. Elige entre más de 20 estilos, incluidos el anime de Studio Ghibli, el manhwa coreano, el manga japonés y la estética popular de dibujos animados con nuestra Transferencia de Estilo de Video."}, "examples": {"title": "Ejemplos de Video to Video AI", "description": "Mira cómo Video to Video AI transforma videos en impresionantes estilos de anime, dibujos animados, manga y manhwa, manteniendo una consistencia de movimiento perfecta con la avanzada Transferencia de Estilo de Video.", "description1": "Cambio de Personaje | prompt: <PERSON>st<PERSON><PERSON><PERSON> a la chica por <PERSON>", "description2": "Cambio de Personaje | prompt: Sust<PERSON><PERSON><PERSON> a la chica por Sailor Moon", "description3": "Transferencia de Estilo | Video de baile real con estilo anime", "originalVideo": "Video Original", "animeVideo": "Video Estilo <PERSON>", "watercolorVideo": "Video Estilo Animación Acuarela", "style": "<PERSON><PERSON><PERSON>", "prompt": "Prompt de E<PERSON>ilo <PERSON>", "description5": "Transferencia de Estilo | Video de baile real con estilo cómic", "description6": "Transformación de Escena | Prompt: Modificar la imagen para que el actor esté caminando en un entorno cyberpunk — una escena de alta ciencia ficción", "animeStyle": "<PERSON><PERSON><PERSON>", "comicStyle": "<PERSON><PERSON><PERSON>", "promptUsed": "Estilo de Prompt Usado", "animeTransformation": "Transformación de Anime", "description7": "Transformación de un perro real al estilo anime, mostrando una mascota realista convertida en un personaje animado", "description8": "Manos tejiendo transformadas al estilo anime, demostrando la preservación detallada del movimiento en actividades artesanales"}, "howTo": {"title": "<PERSON><PERSON><PERSON> Usar Video to Video AI"}, "steps": {"step1": {"title": "Sube Tu Video", "content": "Sube cualquier archivo de video (MP4, MOV, AVI) de hasta 15 segundos y 50MB para el procesamiento de Video a Video AI. Los videos que duren más de 15 segundos se recortan automáticamente para un traspaso de estilo de video óptimo."}, "step2": {"title": "Referencia de Transferencia de Estilo de Video", "content": "Extraemos el primer fotograma y aplicamos el estilo anime, de dibujos animados o manga elegido para crear una guía de referencia para una transformación consistente de Transferencia de Estilo de Video."}, "step3": {"title": "Elige un Estilo Artístico", "content": "Selecciona entre más de 20 estilos preestablecidos como el anime de Studio Ghibli, el manhwa coreano, el manga japonés o crea estilos personalizados con prompts de texto e imágenes de referencia para la Transferencia de Estilo de Video."}, "step4": {"title": "Genera Video to Video AI", "content": "Nuestro Video to Video AI transforma tu video completo utilizando el fotograma de referencia estilizado, preservando al mismo tiempo todo el movimiento, las expresiones y el ritmo originales a través de la Transferencia de Estilo de Video avanzada."}}, "benefits": {"title": "¿Por Qué Usar Video to Video AI?", "description": "Video to Video AI ofrece la Transferencia de Estilo de Video más avanzada con preservación del movimiento, amplias opciones de estilo y precios transparentes."}, "features": {"feature1": {"title": "Preservación Perfecta del Movimiento", "content": "Video to Video AI mantiene cada detalle del movimiento original, las expresiones faciales y el ritmo al aplicar estilos de anime, dibujos animados o manga con una consistencia de Transferencia de Estilo de Video perfecta en cada fotograma."}, "feature2": {"title": "Más de 20 Opciones de Transferencia de Estilo de Video", "content": "Elige entre anime de Studio Ghibli, manhwa coreano, manga japonés, dibujos animados de Disney, estilo <PERSON> y más. Crea Transferencia de Estilo de Video personalizada con prompts de texto o imágenes de referencia utilizando Video to Video AI."}, "feature3": {"title": "Salida de Calidad Profesional", "content": "Genera videos de anime y dibujos animados de alta definición con una aplicación consistente de Transferencia de Estilo de Video, transiciones suaves y sin parpadeo ni artefactos con nuestro Video to Video AI."}, "feature4": {"title": "Sistema de Costos Inteligente", "content": "Precios transparentes con cargos separados para la Transferencia de Estilo de Video y la generación de video. Experimenta con diferentes estilos sin costos de video adicionales utilizando Video to Video AI."}, "feature5": {"title": "Proceso Sencillo de Dos Pasos", "content": "Flujo de trabajo simple de Video to Video AI: sube el video, aplica la Transferencia de Estilo de Video al fotograma de referencia, genera el video completo. No se requiere experiencia técnica con el seguimiento del progreso en tiempo real."}, "feature6": {"title": "Optimización Automática", "content": "Procesamiento inteligente de Video to Video AI con recorte automático, soporte de formato (MP4, MOV, AVI) y cálculo de costos basado en la duración para una Transferencia de Estilo de Video óptima."}}, "faq": {"title": "Preguntas Frecuentes sobre Video to Video AI", "description": "Preguntas comunes sobre nuestra herramienta, proceso, costos y mejores prácticas de Video to Video AI y Transferencia de Estilo de Video.", "q1": "¿Cómo funciona Video to Video AI?", "a1": "Video to Video AI utiliza un proceso de Transferencia de Estilo de Video de dos pasos: 1) Extrae un fotograma de referencia y aplica el estilo anime/dibujos animados elegido, 2) Transforma todo el video utilizando este fotograma estilizado, conservando el movimiento y el ritmo originales. Esto garantiza una aplicación consistente de Transferencia de Estilo de Video en todos los fotogramas.", "q2": "¿<PERSON><PERSON><PERSON>les son los requisitos de formato de video para Video to Video AI?", "a2": "Admitimos formatos MP4, MOV y AVI con un tamaño de archivo máximo de 50MB para el procesamiento de Video a Video AI. Los videos están limitados a 15 segundos y se recortan automáticamente si son más largos, para un traspaso de estilo de video óptimo y eficiencia en costos.", "q3": "¿Cuánto tiempo lleva la Transferencia de Estilo de Video?", "a3": "El procesamiento total de Video to Video AI lleva de 5 a 10 minutos: Transferencia de Estilo de Video (1-3 minutos) y generación de video (3-7 minutos). Puedes supervisar el progreso de la Transferencia de Estilo de Video en tiempo real.", "q4": "¿<PERSON><PERSON><PERSON>les son los costos de Video to Video AI?", "a4": "Los costos se dividen entre la Transferencia de Estilo de Video y la generación de video según la duración. Los costos de Video to Video AI se muestran en tiempo real antes del procesamiento, y los créditos solo se deducen después de la finalización exitosa de la Transferencia de Estilo de Video.", "q5": "¿Puedo crear estilos personalizados de Transferencia de Estilo de Video?", "a5": "¡Sí! Elige entre más de 20 plantillas preestablecidas (Studio Ghibli, manhwa coreano, manga japonés, etc.), escribe prompts de texto personalizados o sube imágenes de referencia para transformaciones únicas de Transferencia de Estilo de Video con nuestro Video to Video AI.", "q6": "¿Qué hace que los videos de entrada sean buenos para Video to Video AI?", "a6": "Los mejores resultados de Transferencia de Estilo de Video se obtienen con sujetos claros, buena iluminación, movimiento estable y características bien definidas. Evita los movimientos rápidos y las imágenes oscuras o borrosas para un procesamiento óptimo de Video to Video AI. Los videos de menos de 5 segundos con personas u objetos claros funcionan mejor para la Transferencia de Estilo de Video."}}