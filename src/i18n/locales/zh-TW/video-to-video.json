{"title": "影片轉影片 AI", "description": "運用影片轉影片 AI 技術，輕鬆轉換您的影片。我們先進的影片風格轉換技術，能將任何影片轉換為動畫、卡通、漫畫及韓漫風格，同時完美保留原始動作與時間軸。", "meta": {"title": "影片轉影片 AI | 最佳影片風格轉換", "description": "運用影片轉影片 AI 技術，輕鬆轉換您的影片。我們先進的影片風格轉換技術，能將任何影片轉換為動畫、卡通、漫畫及韓漫風格，同時完美保留原始動作與時間軸。", "fullDescription": "劃時代的影片轉影片 AI 工具，能將普通影片轉化為動漫傑作、卡通動畫、漫畫風格視覺效果與韓漫畫美學。我們先進的影片風格轉換技術，在應用各種藝術風格轉換時（包括吉卜力工作室動畫、韓國漫畫、日本漫畫、水彩動畫與卡通風格），能確保動作的連貫性。非常適合尋求專業級影片風格轉換的內容創作者、動畫師與藝術家。", "keywords": "影片轉影片 AI, 影片風格轉換, 動漫影片產生器, 卡通影片濾鏡, 漫畫風格轉換, 韓漫畫動畫, 影片轉動漫轉換器, 動畫工具, 卡通影片製作器, 動漫風格影片, 漫畫影片創作者, 影片風格化, 動畫影片產生器, 漫畫影片濾鏡, 動漫轉換, 卡通風格轉換"}, "ui": {"title": "影片轉影片 AI", "tooltip": "使用影片轉影片 AI 和影片風格轉換技術，轉換您的影片", "generatedVideos": "已產生的風格化影片", "emptyState": "您動漫和卡通風格的影片將會顯示在此", "steps": {"uploadVideo": "1. 上傳影片", "styleSelection": "風格選擇", "generateVideo": "3. 產生影片"}, "upload": {"dragText": "點擊上傳或將您的影片拖曳至此", "formatInfo": "支援 MP4, MOV, AVI 格式 • 最長 15 秒 • 最大 50MB", "extractFrameManually": "手動提取首幀", "bestResultsTip": "為獲得最佳效果，請使用單一場景的影片 - 最好以單一鏡頭拍攝。", "safariFormatInfo": "支援 MP4, MOV, AVI 格式 • 最長 15 秒 • 最大 50MB", "safariNotice": "Safari 使用者提示", "safariLimitWarning": "影片長度限制為 15 秒或以內。如需較長影片或自訂時長控制，請使用 Chrome 瀏覽器。", "bestResultsTip1": "為獲得最佳效果，請使用一次連續拍攝的影片。", "bestResultsTip2": "如影片中有角色，請確保第一幀顯示正面。理想情況下，影片應集中於一位主要角色，並至少包含完整的上半身。"}, "duration": {"title": "持續時間", "description": "選擇影片產生的持續時間。較長的持續時間會消耗更多 Zaps。", "originalDuration": "原始：{{duration}} 秒", "tooLong": "過長", "willBeTrimmed": "將從 {{original}} 秒修剪為 {{target}} 秒", "originalLength": "原始長度", "safariNote": "檢測到 Safari：使用原始視頻長度以確保最佳兼容性", "chromeAdvice": "如需自定義時長控制，請使用 Chrome 瀏覽器", "safariUseOriginal": "Safari 使用者：視頻將使用原始時長以確保最佳兼容性"}, "videoMode": {"title": "產生模式", "human": "人像影片模式", "humanDescription": "針對人物主體與人像影片進行最佳化", "general": "通用模式", "generalDescription": "適用於任何主體與場景類型"}, "videoPrompt": {"title": "提示 (可選)", "placeholder": "例如：跳舞的動漫女孩", "description": "新增更多細節以引導影片產生過程"}, "framePreview": {"original": "原始", "styled": "已風格化", "applyingStyle": "正在套用風格...", "awaitingStyle": "等待風格", "selectStyleBelow": "在下方選擇一種風格", "beforeAfterComparison": "風格轉換前後比較", "applyingStyleToFrame": "正在將您選擇的風格套用於幀...", "frameReferenceText": "此幀將作為影片風格轉換的參考", "styleTooltip": "此風格化的幀將引導整個影片轉換。"}, "styleModes": {"templates": "範本", "prompt": "提示", "reference": "參考"}, "styleTemplates": {"anime": "動漫", "ghibliAnime": "吉卜力動漫", "koreanManhwa": "韓國漫畫", "cartoon": "卡通", "manga": "漫畫", "inkWash": "水墨", "watercolor": "水彩", "lineArt": "線條藝術", "lowPoly": "低多邊形", "clay": "黏土動畫", "pixelArt": "像素藝術", "origami": "摺紙", "lego": "樂高", "vaporwave": "蒸氣波", "rickAndMorty": "瑞克和莫蒂", "southPark": "南方公園", "simpsons": "辛普森家庭", "naruto": "火影忍者", "onePiece": "航海王", "myLittlePony": "彩虹小馬", "comic": "漫畫", "miku": "未來", "barbie": "芭比", "goku": "悟空（七龍珠）", "trump": "川普", "princess": "公主 / 王子", "kimono": "和服 / 浴衣", "superhero": "超級英雄", "magicalGirl": "魔法少女", "hogwarts": "霍格華茲", "cowboy": "牛仔", "sailorUniform": "水手服", "pixar": "皮克斯", "apocalypse": "末日", "magicalWorld": "魔法世界", "dreamland": "夢境", "cyberpunk": "賽博朋克", "kpopIdol": "韓流偶像", "cloud": "雲朵", "mars": "火星", "outerSpace": "外太空", "sailorMoon": "美少女戰士", "pencilSketch": "鉛筆畫", "retroGame": "復古遊戲", "mobileGame": "手機遊戲", "psGame": "PS 遊戲", "underwater": "水下世界", "snow": "冰雪", "toyBricks": "積木風格", "skeleton": "骷髏造型", "fire": "火焰效果", "muscle": "肌肉線條", "metal": "金屬質感", "crystal": "水晶透明", "westernAnimation": "西方動畫風格", "vanGogh": "梵高風格", "oilPainting": "油畫效果"}, "prompt": {"placeholder": "描述您想要的風格轉換...", "example": "範例：「將這個男人變成鳴人」、「將這個變成經典的 90 年代動漫風格」、「讓這個女孩穿上花裙」"}, "reference": {"uploadText": "上傳您預先風格化的幀作為參考", "formatInfo": "支援 JPG, PNG, JPEG, WEBP • 最大 10MB", "compositionWarning": "請確保參考影像與原始影片第一幀的構圖完全一致。"}, "buttons": {"applying": "正在套用...", "useNewReference": "使用新的參考", "applyNewStyle": "套用新的風格", "useReference": "使用參考", "applyStyle": "套用風格", "generateVideo": "產生影片", "generatingVideo": "正在產生影片...", "generateMore": "產生更多影片", "createAnother": "建立另一個影片"}, "separators": {"readyToGenerate": "準備好產生影片"}, "styleCategories": {"tStyleTransfer": "風格轉換", "changeMaterial": "材質變換", "changeEnvironment": "環境變換", "cosplay": "角色扮演"}}, "whatIs": {"title": "什麼是影片轉影片 AI？", "description": "影片轉影片 AI 運用先進的影片風格轉換技術，將普通影片轉換為動畫、卡通、漫畫與韓漫畫風格的動畫。我們的兩步驟流程首先將您選擇的風格套用於參考幀，然後使用此風格化的幀來轉換您的整個影片，同時保留原始動作與時間軸。透過我們的影片風格轉換，從 20 多種風格中選擇，包括吉卜力工作室動畫、韓國漫畫、日本漫畫與流行的卡通美學。"}, "examples": {"title": "影片轉影片 AI 範例", "description": "瞭解我們的影片轉影片 AI 如何將影片轉換為令人驚豔的動畫、卡通、漫畫與韓漫畫風格，同時透過先進的影片風格轉換，維持完美的動作一致性。", "description1": "角色替換 | 提示：將女孩換成唐納·川普", "description2": "角色替換 | 提示：將女孩換成美少女戰士", "description3": "風格轉換 | 將真實舞蹈影片轉換為動漫風格", "originalVideo": "原始影片", "animeVideo": "動畫風格影片", "watercolorVideo": "水彩動畫風格", "style": "已套用風格", "prompt": "使用的風格提示", "description5": "風格轉換 | 將真實舞蹈影片轉換為漫畫風格", "description6": "場景轉換 | 提示：將畫面改為演員在賽博朋克環境中行走——一個高度科幻的場景", "animeStyle": "動漫風格", "comicStyle": "漫畫風格", "promptUsed": "使用的風格提示", "animeTransformation": "動畫變形", "description7": "將現實生活中的狗轉換為動畫風格，展示如何將真實寵物變為動畫角色", "description8": "將手工編織轉換為動畫風格，展示在工藝活動中細緻動作的保留"}, "howTo": {"title": "如何使用影片轉影片 AI"}, "steps": {"step1": {"title": "上傳您的影片", "content": "上傳任何影片檔案（MP4, MOV, AVI），長度最長 15 秒且不超過 50MB，以進行影片到影片的 AI 處理。超過 15 秒的影片將自動裁剪，以達到最佳的影片風格轉換效果。"}, "step2": {"title": "影片風格轉換參考", "content": "我們提取第一幀並套用您選擇的動畫、卡通或漫畫風格，以建立一個參考指南，用於一致的影片風格轉換。"}, "step3": {"title": "選擇藝術風格", "content": "從 20 多種預設風格中選擇，例如吉卜力工作室動畫、韓國漫畫、日本漫畫，或使用文字提示與參考影像建立自訂風格，以進行影片風格轉換。"}, "step4": {"title": "產生影片轉影片 AI", "content": "我們的影片轉影片 AI 使用風格化的參考幀轉換您的完整影片，同時透過先進的影片風格轉換，保留所有原始動作、表情與時間軸。"}}, "benefits": {"title": "為什麼要使用影片轉影片 AI", "description": "我們的影片轉影片 AI 提供最先進的影片風格轉換，具備動作保留、廣泛的風格選項與透明的定價。"}, "features": {"feature1": {"title": "完美的動作保留", "content": "影片轉影片 AI 在套用動畫、卡通或漫畫風格時，透過幀完美的影片風格轉換一致性，維持原始動作、面部表情與時間軸的每個細節。"}, "feature2": {"title": "20 多種影片風格轉換選項", "content": "從吉卜力工作室動畫、韓國漫畫、日本漫畫、迪士尼卡通、火影忍者風格等選擇。使用影片轉影片 AI，透過文字提示或參考影像建立自訂影片風格轉換。"}, "feature3": {"title": "專業品質輸出", "content": "使用我們的影片轉影片 AI，產生具有一致的影片風格轉換應用、平滑過渡且無閃爍或瑕疵的高畫質動畫與卡通影片。"}, "feature4": {"title": "智慧成本系統", "content": "透明的定價，影片風格轉換與影片產生分別收費。使用影片轉影片 AI，在不增加額外影片成本的情況下，試驗不同的風格。"}, "feature5": {"title": "簡單的兩步驟流程", "content": "簡單的影片轉影片 AI 工作流程：上傳影片，將影片風格轉換套用於參考幀，產生完整影片。無需技術專業知識，並具有即時進度追蹤。"}, "feature6": {"title": "自動最佳化", "content": "智慧影片轉影片 AI 處理，具有自動修剪、格式支援 (MP4, MOV, AVI) 與基於持續時間的成本計算，以實現最佳影片風格轉換。"}}, "faq": {"title": "影片轉影片 AI 常見問題", "description": "有關我們的影片轉影片 AI 和影片風格轉換工具、流程、成本與最佳實踐的常見問題。", "q1": "影片轉影片 AI 如何運作？", "a1": "我們的影片轉影片 AI 使用兩步驟的影片風格轉換流程：1) 提取參考幀並套用您選擇的動畫/卡通風格，2) 使用此風格化的幀轉換整個影片，同時保留原始動作與時間軸。這確保所有幀上一致的影片風格轉換應用。", "q2": "影片轉影片 AI 的影片格式要求是什麼？", "a2": "我們支援 MP4, MOV 和 AVI 格式，檔案大小上限為 50MB，適用於影片到影片的 AI 處理。影片限制為 15 秒，若超過將自動裁剪，以達到最佳影片風格轉換效果和成本效益。", "q3": "影片風格轉換需要多長時間？", "a3": "總共的影片轉影片 AI 處理需要 5-10 分鐘：影片風格轉換 (1-3 分鐘) 與影片產生 (3-7 分鐘)。您可以即時監控影片風格轉換的進度。", "q4": "影片轉影片 AI 的費用是多少？", "a4": "影片風格轉換與影片產生根據持續時間分別收費。在處理之前，會即時顯示影片轉影片 AI 的成本，並且僅在成功完成影片風格轉換後才會扣除積分。", "q5": "我可以建立自訂影片風格轉換風格嗎？", "a5": "可以！從 20 多個預設範本（吉卜力工作室、韓國漫畫、日本漫畫等）中選擇，編寫自訂文字提示，或上傳參考影像，以使用我們的影片轉影片 AI 進行獨特的影片風格轉換。", "q6": "什麼樣的輸入影片適合影片轉影片 AI？", "a6": "透過清晰的主體、良好的光照、穩定的動作與明確定義的特徵，獲得最佳的影片風格轉換效果。避免快速移動、黑暗或模糊的鏡頭，以實現最佳的影片轉影片 AI 處理。少於 5 秒且帶有人物或清晰物體的影片最適合影片風格轉換。"}}