{"title": "Video to Video AI", "description": "Преобразите свои видео с помощью технологии Video to Video AI. Наш продвинутый инструмент переноса стиля видео преобразует любое видео в стили аниме, мультфильмов, манги и манхвы, сохраняя оригинальное движение и тайминг.", "meta": {"title": "Video to Video AI | Лучший перенос стиля видео", "description": "Преобразите свои видео с помощью технологии Video to Video AI. Наш продвинутый инструмент переноса стиля видео преобразует любое видео в стили аниме, мультфильмов, манги и манхвы, сохраняя оригинальное движение и тайминг.", "fullDescription": "Революционный инструмент Video to Video AI, который превращает обычные видео в шедевры в стилях аниме, мультфильмов, манги и манхвы. Наша передовая технология переноса стиля видео сохраняет плавность движений, применяя при этом художественные преобразования, включая стили аниме Studio Ghibli, корейской манхвы, японской манги, акварельной анимации и мультфильмов. Идеально подходит для создателей контента, аниматоров и художников, которым требуется профессиональный перенос стиля видео.", "keywords": "Video to Video AI, Перенос стиля видео, Генератор аниме-виде<PERSON>, Фильтр мультфильмов, Перенос стиля манги, Анимация манхвы, Конвертер видео в аниме, Инструмент для анимации, Создатель мультфильмов, Видео в стиле аниме, Создатель видео в стиле манги, Стилизация видео, Генератор анимированных видео, Фильтр комиксов, Преобразование в аниме, Перенос стиля мультфильмов"}, "ui": {"title": "Video to Video AI", "tooltip": "Преобразите свое видео с помощью технологии Video to Video AI и переноса стиля видео", "generatedVideos": "Созданные стилизованные видео", "emptyState": "Здесь появятся ваши видео в стиле аниме и мультфильмов", "steps": {"uploadVideo": "1. Загрузите видео", "styleSelection": "Выберите стиль", "generateVideo": "3. Создайте видео"}, "upload": {"dragText": "Нажмите, чтобы загрузить, или перетащите видео сюда", "formatInfo": "Поддерживаются форматы MP4, MOV, AVI • Максимальная длина 15 секунд • Максимальный размер 50MB", "extractFrameManually": "Извлечь первый кадр вручную", "bestResultsTip": "Для достижения наилучших результатов используйте видео с одной сценой, желательно снятое одним дублем.", "safariFormatInfo": "Поддерживаются форматы MP4, MOV, AVI • Максимальная длина 15 секунд • Максимальный размер 50MB", "safariNotice": "Сообщение для пользователей Safari", "safariLimitWarning": "В Safari длина видео ограничена до 15 секунд. Для обработки более длинных видео используйте браузер Chrome.", "bestResultsTip1": "Для достижения наилучших результатов используйте видео, снятое в одном непрерывном кадре.", "bestResultsTip2": "Если участвует человек, на первом кадре должно быть представлено фронтальное изображение. Идеально, если в видео будет один главный актер, и хотя бы верхняя часть тела будет видна полностью."}, "duration": {"title": "Длительность", "description": "Выберите продолжительность создаваемого видео. Чем больше длительность, тем больше zaps требуется.", "originalDuration": "Исходная: {{duration}} с", "tooLong": "Слишком длинное", "willBeTrimmed": "Будет обрезано с {{original}} с до {{target}} с", "originalLength": "Исходная длительность", "safariNote": "Обна<PERSON><PERSON><PERSON><PERSON><PERSON> Safari: Используется исходная длина видео для лучшей совместимости", "chromeAdvice": "Для изменения длительности видео используйте браузер Chrome", "safariUseOriginal": "Пользователи Safari: Видео будет проигрываться с исходной длительностью для оптимальной совместимости"}, "videoMode": {"title": "Режим генерации", "human": "Режим видео с людьми", "humanDescription": "Оптимизирован для видео с людьми и портретов", "general": "Общий режим", "generalDescription": "Подходит для любых объектов и сцен"}, "videoPrompt": {"title": "Подсказка (необязательно)", "placeholder": "Например, танцующая аниме-девушка", "description": "Добавьте детали, чтобы направить процесс создания видео"}, "framePreview": {"original": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "styled": "Стилизовано", "applyingStyle": "Применяем стиль...", "awaitingStyle": "Ожид<PERSON><PERSON>м стиль", "selectStyleBelow": "Выберите стиль ниже", "beforeAfterComparison": "Сравнение переноса стиля до и после", "applyingStyleToFrame": "Применяем выбранный вами стиль к кадру...", "frameReferenceText": "Этот кадр будет использоваться в качестве ориентира для переноса стиля видео", "styleTooltip": "Этот стилизованный кадр будет определять преобразование видео."}, "styleModes": {"templates": "Шаблоны", "prompt": "Подсказка", "reference": "Ссылка"}, "styleTemplates": {"anime": "Аниме", "ghibliAnime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "koreanManhwa": "Корейская манхва", "cartoon": "Мультфильм", "manga": "Манга", "inkWash": "Тушь", "watercolor": "Акварель", "lineArt": "Линии", "lowPoly": "Низкий полигон", "clay": "Пластилиновая анимация", "pixelArt": "Пиксель-арт", "origami": "Бумага оригами", "lego": "Lego", "vaporwave": "Vaporwave", "rickAndMorty": "Рик и Морти", "southPark": "Южный парк", "simpsons": "Симпсоны", "naruto": "Наруто", "onePiece": "One Piece", "myLittlePony": "My Little Pony", "comic": "<PERSON>о<PERSON><PERSON><PERSON><PERSON>", "miku": "<PERSON>и<PERSON><PERSON>", "barbie": "Барби", "goku": "Гоку (Dragon Ball)", "trump": "Дональд Трамп", "princess": "Принц/Принцесса", "kimono": "Кимоно / Юката", "superhero": "Супергерой", "magicalGirl": "Волшебная девочка", "hogwarts": "Хогвартс", "cowboy": "Ковбой", "sailorUniform": "Морская форма", "pixar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apocalypse": "Апокалипсис", "magicalWorld": "Волшебный мир", "dreamland": "Стр<PERSON><PERSON> снов", "cyberpunk": "К<PERSON>б<PERSON>р<PERSON><PERSON>нк", "kpopIdol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cloud": "Облако", "mars": "<PERSON><PERSON><PERSON><PERSON>", "outerSpace": "Космос", "sailorMoon": "Се<PERSON><PERSON>о<PERSON> Мун", "pencilSketch": "Набросок карандашом", "retroGame": "Ретро-игра", "mobileGame": "Мобильная игра", "psGame": "Игра для PS", "underwater": "Подводный мир", "snow": "Зим<PERSON>ий пейзаж", "toyBricks": "Конструктор", "skeleton": "Скелет", "fire": "Пламя", "muscle": "Мускулы", "metal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crystal": "Крис<PERSON><PERSON><PERSON><PERSON>", "westernAnimation": "Западная анимация", "vanGogh": "Стиль Ван Гога", "oilPainting": "Масляная живопись"}, "prompt": {"placeholder": "Опишите желаемый стиль преобразования...", "example": "Пример: «Замените мужчину на Наруто», «Превратите в классический стиль аниме 90-х», «Оденьте девушку в цветочное платье»"}, "reference": {"uploadText": "Загрузите предварительно стилизованный кадр в качестве эталона", "formatInfo": "Поддерживаемые форматы: JPG, PNG, JPEG, WEBP • Макс. 10 МБ", "compositionWarning": "Убедитесь, что композиция эталонного изображения соответствует первому кадру исходного видео."}, "buttons": {"applying": "Применяем...", "useNewReference": "Использовать новую ссылку", "applyNewStyle": "Применить новый стиль", "useReference": "Использовать ссылку", "applyStyle": "Применить стиль", "generateVideo": "Создать видео", "generatingVideo": "Создаем видео...", "generateMore": "Создать больше видео", "createAnother": "Создать другое видео"}, "separators": {"readyToGenerate": "Готовы создать видео"}, "styleCategories": {"tStyleTransfer": "Стилевое преобразование", "changeMaterial": "Сменить материал", "changeEnvironment": "Сменить окружение", "cosplay": "Косплей"}}, "whatIs": {"title": "Что такое Video to Video AI?", "description": "Video to Video AI преобразует обычные видео в анимации в стиле аниме, мультфильмов, манги и манхвы, используя передовую технологию переноса стиля видео. Наш двухэтапный процесс сначала применяет выбранный вами стиль к эталонному кадру, а затем использует его для преобразования всего видео, сохраняя оригинальное движение и тайминг. Выберите один из 20+ стилей, включая аниме Studio Ghibli, корейскую манхву, японскую мангу и популярные мультяшные стили, с помощью нашего переноса стиля видео."}, "examples": {"title": "Примеры Video to Video AI", "description": "Посмотрите, как наш Video to Video AI преобразует видео в потрясающие стили аниме, мультфильмов, манги и манхвы, сохраняя идеальную плавность движений с помощью усовершенствованного переноса стиля видео.", "description1": "Персонаж замена | Совет: Замените девушку на Дональда Трампа", "description2": "Персонаж замена | Совет: Замените девушку на Сейлор Мун", "description3": "Смена стиля | Реальное видео танца в аниме-стиль", "originalVideo": "Оригинальное видео", "animeVideo": "Видео в стиле аниме", "watercolorVideo": "Акварельный стиль анимации", "style": "Примененный стиль", "prompt": "Использованная подсказка стиля", "description5": "Смена стиля | Реальное видео танца в комический стиль", "description6": "Трансформация сцены | Совет: Измените изображение так, чтобы актер оказался в киберпанк-окружении — очень научно-фантастическая сцена", "animeStyle": "Аниме-стиль", "comicStyle": "Комический стиль", "promptUsed": "Используемая стилестическая подсказка", "animeTransformation": "Преобразование в стиле аниме", "description7": "Преобразование собаки из реальной жизни в стиль аниме, демонстрирующее реалистичное превращение питомца в анимированного персонажа", "description8": "Преобразование рук, вяжущих спицами, в стиль аниме, демонстрирующее детальную передачу движений в ремесленных занятиях"}, "howTo": {"title": "Как использовать Video to Video AI"}, "steps": {"step1": {"title": "Загрузите видео", "content": "Загрузите виде<PERSON><PERSON><PERSON><PERSON><PERSON> (MP4, MOV, AVI) длиной до 15 секунд и размером до 50MB для обработки в Видео AI. Видео длиной более 15 секунд будут автоматически обрезаны для оптимальной обработки стиль-передачи."}, "step2": {"title": "Эталон для переноса стиля видео", "content": "Мы извлекаем первый кадр и применяем выбранный вами стиль аниме, мультфильма или манги, чтобы создать эталон для переноса стиля видео."}, "step3": {"title": "Выберите художественный стиль", "content": "Выберите один из 20+ предустановленных стилей, таких как аниме Studio Ghibli, корейская манхва, японская манга, или создайте собственные стили с помощью текстовых подсказок и эталонных изображений для переноса стиля видео."}, "step4": {"title": "Создайте видео в Video AI", "content": "Наш Video to Video AI преобразует ваше полное видео, используя стилизованный эталонный кадр, сохраняя при этом все исходные движения, выражения и тайминг с помощью расширенного переноса стиля видео."}}, "benefits": {"title": "Зачем использовать Video to Video AI", "description": "Наш Video to Video AI предлагает самый продвинутый перенос стиля видео с сохранением движения, широкими возможностями стилизации и прозрачными ценами."}, "features": {"feature1": {"title": "Идеальное сохранение движения", "content": "Video to Video AI сохраняет каждую деталь исходного движения, выражения лица и тайминга, применяя стили аниме, мультфильмов или манги с идеальным переносом стиля видео."}, "feature2": {"title": "20+ вариантов переноса стиля видео", "content": "Выберите из аниме Studio Ghibli, корейской манхвы, японской манги, мультфильм<PERSON> Disney, стиля Наруто и других. Создавайте собственный перенос стиля видео с помощью текстовых подсказок или эталонных изображений с использованием Video to Video AI."}, "feature3": {"title": "Вывод профессионального качества", "content": "Создавайте аниме и мультфильмы в высоком разрешении с последовательным применением переноса стиля видео, плавными переходами и отсутствием мерцания или артефактов с помощью нашего Video to Video AI."}, "feature4": {"title": "Умная система оплаты", "content": "Прозрачная система оплаты с отдельной платой за перенос стиля видео и создание видео. Экспериментируйте с разными стилями без дополнительных затрат на видео с помощью Video to Video AI."}, "feature5": {"title": "Простой двухэтапный процесс", "content": "Простой рабочий процесс Video to Video AI: загрузите видео, примените перенос стиля видео к эталонному кадру, создайте полное видео. Никаких технических знаний не требуется, отслеживайте прогресс в реальном времени."}, "feature6": {"title": "Автоматическая оптимизация", "content": "Интеллектуальная обработка Video to Video AI с автоматической обрезкой, поддержкой форматов (MP4, MOV, AVI) и расчетом стоимости в зависимости от продолжительности для оптимального переноса стиля видео."}}, "faq": {"title": "Часто задаваемые вопросы о Video to Video AI", "description": "Ответы на часто задаваемые вопросы о нашем инструменте Video to Video AI и переносе стиля видео, процессе, стоимости и лучших практиках.", "q1": "Как работает Video to Video AI?", "a1": "Наш Video to Video AI использует двухэтапный процесс переноса стиля видео: 1) Извлеките эталонный кадр и примените выбранный вами стиль аниме/мультфильма, 2) Преобразуйте все видео, используя этот стилизованный кадр, сохраняя оригинальное движение и тайминг. Это обеспечивает согласованное применение переноса стиля видео для всех кадров.", "q2": "Какие требования к формату видео для Video to Video AI?", "a2": "Мы поддерживаем MP4, MOV и AVI форматы с максимальным размером файла 50MB для использования в Видео AI. Видео ограничены длиной 15 секунд и автоматически обрезаются, если длиннее, для наилучшего результата и экономии средств.", "q3": "Сколько времени занимает перенос стиля видео?", "a3": "Общая обработка Video to Video AI занимает 5-10 минут: перенос стиля видео (1-3 минуты) и создание видео (3-7 минут). Вы можете отслеживать ход переноса стиля видео в режиме реального времени.", "q4": "Какова стоимость Video to Video AI?", "a4": "Отдельная плата за перенос стиля видео и создание видео в зависимости от продолжительности. Стоимость Video to Video AI отображается в режиме реального времени перед обработкой, а кредиты списываются только после успешного завершения переноса стиля видео.", "q5": "Могу ли я создавать собственные стили переноса стиля видео?", "a5": "Да! Выберите один из 20+ предустановленных шаблонов (Studio Ghibli, корейская манхва, японская манга и т. д.), напишите собственные текстовые подсказки или загрузите эталонные изображения для уникальных преобразований переноса стиля видео с помощью нашего Video to Video AI.", "q6": "Какие видео лучше всего подходят для Video to Video AI?", "a6": "Наилучшие результаты переноса стиля видео достигаются с четкими объектами, хорошим освещением, стабильным движением и четко определенными чертами. Избегайте быстрых движений, темных или размытых кадров для оптимальной обработки Video to Video AI. Видео продолжительностью менее 5 секунд с людьми или четкими объектами лучше всего подходят для переноса стиля видео."}}