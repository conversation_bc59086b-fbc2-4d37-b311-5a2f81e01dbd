// 前端语言常量，从后端常量导出的精简版本

// UI language options (for dropdowns)
export const LANGUAGE_OPTIONS = [
  { code: 'en', name: 'English' },
  { code: 'es', name: '<PERSON><PERSON>a<PERSON><PERSON>' },
  { code: 'ja', name: '日本語' },
  { code: 'zh-CN', name: '简体中文' },
  { code: 'zh-TW', name: '繁體中文' },
  { code: 'ko', name: '한국어' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>' },
  { code: 'fr', name: 'Fran<PERSON>' },
  { code: 'pt', name: 'Portuguê<PERSON>' },
  { code: 'id', name: 'Bahasa Indonesia' },
  { code: 'hi', name: 'हिंदी' },
  { code: 'ru', name: 'Русский' },
  { code: 'vi', name: 'Tiếng Việt' },
  { code: 'th', name: 'ไทย' },
];

// Language code to localized name mapping
export const LANGUAGE_TO_LOCAL_NAME: { [key: string]: string } = {
  'zh-CN': '简体中文',
  'zh-TW': '繁體中文',
  ja: '日本語',
  ko: '한국어',
  es: 'Español',
  fr: 'Français',
  de: 'Deutsch',
  pt: 'Português',
  ru: 'Русский',
  vi: 'Tiếng Việt',
  th: 'ไทย',
  hi: 'हिंदी',
  id: 'Bahasa Indonesia',
  en: 'English',
};