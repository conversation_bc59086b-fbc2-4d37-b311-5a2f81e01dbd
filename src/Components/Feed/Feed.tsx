/* eslint-disable */
import { useRouter } from 'next/router';
import {
  useState,
  useEffect,
  use<PERSON><PERSON>back,
  ChangeEventHandler,
  useRef,
} from 'react';
import React from 'react';
import {
  Button,
  useDisclosure,
  Spinner,
  Skeleton,
  Card,
  CardBody,
  CardFooter,
} from '@nextui-org/react';
import {
  isMobileAtom,
  postListAtom,
  pageAtom,
  tags,
  feedTagAtom,
  Post,
  Comment,
  profileAtom,
} from '../../state';

import { useAtom, useAtomValue } from 'jotai';
import mixpanel from 'mixpanel-browser';
import { useTranslation } from 'react-i18next';

import dynamic from 'next/dynamic'; // next-image optimization
import useInfiniteScroll from './useInfiniteScrollNew'; // adjust the path as necessary
import { PostCard } from './PostCard';
import Masonry from 'react-masonry-css';
import { cn } from '@/lib/utils';
import { PWAInstallPost } from './PWAInstallPost';
import { ActivityPosts, ACTIVITY_POSTS_COUNT } from './ActivityPosts';

const DynamicImage = dynamic(
  () => import('@nextui-org/react').then(mod => mod.Image),
  { ssr: false },
);

// 定义 API tag 的接口
interface ApiTag {
  id: number;
  name: string;
  popularity: number;
}

// 加载中的占位图组件
const PostCardSkeleton = ({ index = 0 }: { index?: number }) => {
  const heights = [280, 320, 250, 300, 350, 270];
  const height = heights[index % heights.length];

  return (
    <div className='caffelabs text-foreground'>
      <Card className='mt-1 bg-transparent rounded-xl shadow-none'>
        <CardBody className='overflow-visible p-0 bg-white rounded-xl'>
          <Skeleton className='rounded-xl'>
            <div
              className='rounded-xl bg-default-300'
              style={{ height: `${height}px` }}></div>
          </Skeleton>
        </CardBody>
        <CardFooter className='justify-between pt-2 pr-3 pb-1 pl-3 text-sm'>
          <Skeleton className='rounded-lg w-4/5'>
            <div className='h-5 rounded-lg bg-default-200'></div>
          </Skeleton>
        </CardFooter>
        <CardFooter className='justify-between pt-0 pr-3 pb-3 pl-3 text-small'>
          <div className='flex items-center align-center'>
            <Skeleton className='rounded-full'>
              <div className='w-5 h-5 rounded-full bg-default-200'></div>
            </Skeleton>
            <Skeleton className='ml-1 rounded-lg'>
              <div className='h-4 w-24 rounded-lg bg-default-200'></div>
            </Skeleton>
          </div>
          <div className='flex flex-col gap-1 items-start align-center'>
            <Skeleton className='rounded-lg'>
              <div className='h-4 w-12 rounded-lg bg-default-200'></div>
            </Skeleton>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

const filterNSFW = (posts: Post[], selectedApiTag: number | null) => {
  if (selectedApiTag === 2) {
    return posts;
  }
  return posts.filter(post => post.post_tags.some(tag => tag.id !== 2));
};

// Helper function to calculate the activity post index
const calculateActivityPostIndex = (index: number): number => {
  return Math.floor((index - 1) / 3);
};

const Feed = ({
  simple = false,
  tagId,
  prerenderedPosts = true,
  className,
  forceNavigation = false,
  isCharacterFeed = false,
}: {
  simple?: boolean;
  tagId?: number;
  prerenderedPosts?: boolean;
  className?: string;
  forceNavigation?: boolean;
  isCharacterFeed?: boolean;
}) => {
  const router = useRouter();
  const { t } = useTranslation('feed');
  const [list, setList] = useAtom(postListAtom);
  const [page, setPage] = useAtom(pageAtom);
  const [selectedTag, setSelectedTag] = useAtom(feedTagAtom);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [activeItemId, setActiveItemId] = useState<number | null>(null);
  const [comment, setComment] = useState('');
  const [isMobile, setIsMobile] = useAtom(isMobileAtom);
  const profile = useAtomValue(profileAtom);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initialLoadError, setInitialLoadError] = useState<string | null>(null);
  const [paginationError, setPaginationError] = useState<string | null>(null);

  const [apiTags, setApiTags] = useState<ApiTag[]>([]);
  const [selectedApiTag, setSelectedApiTag] = useState<number | null>(
    tagId ?? null,
  );
  const [isLoadingTags, setIsLoadingTags] = useState(false);
  const isLoadingRef = useRef(false);

  // Pinned posts state
  const [pinnedPosts, setPinnedPosts] = useState<Post[]>([]);
  const [isLoadingPinnedPosts, setIsLoadingPinnedPosts] = useState(false);

  // const initialLoadCompleteRef = useRef<{ [key: string]: boolean }>({});
  // const emptyPagesCounter = useRef(0);
  // const maxEmptyPagesAllowed = 3;
  // const hasAttemptedInitialLoad = useRef<{ [key: string]: boolean }>({});

  // Helper function to determine if we should insert an activity post
  const shouldInsertActivityPost = (index: number): boolean => {
    return (
      selectedTag === 'Trending' &&
      !isCharacterFeed &&
      index % 3 === 1 &&
      Math.floor((index - 1) / 3) < ACTIVITY_POSTS_COUNT &&
      !selectedApiTag
    );
  };

  const paginationRef = useRef({
    pageNo: page,
    pageSize: 15,
  });

  useEffect(() => {
    if (router.query.tag_id) {
      const tagId = Number(router.query.tag_id);
      if (tagId) {
        setSelectedApiTag(tagId);
      }
    }
  }, [router.query.tag_id]);

  // refresh oc relative post
  useEffect(() => {
    if (tagId && isCharacterFeed) {
      setList([]);
    }
  }, [tagId, isCharacterFeed]);

  // 获取置顶贴文
  const fetchPinnedPosts = useCallback(async () => {
    if (selectedTag !== 'Trending' || isCharacterFeed || selectedApiTag) {
      return;
    }

    try {
      setIsLoadingPinnedPosts(true);
      // 获取当前语言设置
      const currentLang = localStorage.getItem('lang') || 'en';

      const response = await fetch('/api/pinPost?action=list', {
        headers: {
          'Accept-Language': currentLang,
        },
      });

      if (response.ok) {
        const pinnedData = await response.json();
        setPinnedPosts(pinnedData || []);
      } else {
        setPinnedPosts([]);
      }
    } catch (error) {
      setPinnedPosts([]);
    } finally {
      setIsLoadingPinnedPosts(false);
    }
  }, [selectedTag, isCharacterFeed, selectedApiTag]);

  // 获取置顶贴文
  useEffect(() => {
    fetchPinnedPosts();
  }, [fetchPinnedPosts]);

  // 获取 API tags
  const fetchApiTags = useCallback(async () => {
    try {
      setIsLoadingTags(true);
      const searchParams = new URLSearchParams();
      searchParams.set('size', '20');
      if (router.query.tag_id || tagId) {
        searchParams.set(
          'include_ids',
          [router.query.tag_id, tagId].filter(t => !!t).join(','),
        );
      }

      const response = await fetch(`/api/tags?${searchParams.toString()}`);
      if (response.ok) {
        const result = await response.json();
        if (result.code) {
          setApiTags(result.data);
        }
      } else {
        console.error('Failed to fetch tags');
      }
    } catch (error) {
      console.error('Error fetching tags:', error);
    } finally {
      setIsLoadingTags(false);
    }
  }, [router.query.tag_id, tagId]);

  // 在组件加载时获取 tags
  useEffect(() => {
    fetchApiTags();
  }, [fetchApiTags]);

  // 先定义fetchMoreData函数，但不使用useCallback
  // const fetchMoreData = async (isInitial = false) => {
  //   // 创建缓存键，组合排序和筛选条件
  //   const cacheKey = selectedApiTag
  //     ? `${selectedTag}_api_tag_${selectedApiTag}`
  //     : selectedTag;
  //   try {
  //     // 使用 ref 检查是否正在加载，防止重复调用
  //     if (isLoadingRef.current) {
  //       return;
  //     }

  //     // 如果是初始加载，但已经完成过初始加载，则跳过
  //     if (isInitial && initialLoadCompleteRef.current[cacheKey]) {
  //       console.log(
  //         `Initial load already completed for cache key ${cacheKey}, skipping`,
  //       );
  //       return;
  //     }

  //     // 如果没有更多数据或正在加载，不执行任何操作
  //     if (!hasMore && !isInitial) {
  //       return;
  //     }

  //     setIsLoading(true);
  //     isLoadingRef.current = true;

  //     if (isInitial) {
  //       hasAttemptedInitialLoad.current[cacheKey] = true;
  //       emptyPagesCounter.current = 0;
  //       setInitialLoadError(null);
  //       setPaginationError(null);
  //       setPage(1);
  //     } else {
  //       setIsLoadingMore(true);
  //       setPaginationError(null);
  //     }

  //     const currentPage = isInitial ? 1 : page + 1;

  //     // 构建 API URL，组合排序和筛选参数
  //     let apiUrl = `/api/fetchFeed?page=${currentPage}&sortby=${selectedTag}&mainfeedonly=True`;
  //     if (selectedApiTag) {
  //       apiUrl += `&tag=${selectedApiTag}`;
  //     }

  //     console.log(`Fetching from: ${apiUrl}`);

  //     // 添加超时控制
  //     const controller = new AbortController();
  //     const timeoutId = setTimeout(() => controller.abort(), 10000);

  //     const response = await fetch(apiUrl, {
  //       signal: controller.signal,
  //     });

  //     clearTimeout(timeoutId);

  //     if (!response.ok) {
  //       const errorText = await response
  //         .text()
  //         .catch(() => t('errors.unknownError', 'Unknown error'));
  //       console.error(`API error (${response.status}): ${errorText}`);
  //       throw new Error(
  //         t('errors.serverError', 'Server error {{status}}: {{statusText}}', {
  //           status: response.status,
  //           statusText: response.statusText,
  //         }),
  //       );
  //     }

  //     const newData = await response.json();
  //     console.log(
  //       `Fetched page ${currentPage}, got ${newData ? newData.length : 0} items`,
  //     );

  //     if (!newData || !Array.isArray(newData) || newData.length === 0) {
  //       console.log(
  //         `Empty page received (${emptyPagesCounter.current + 1}/${maxEmptyPagesAllowed})`,
  //       );

  //       emptyPagesCounter.current++;

  //       if (isInitial) {
  //         if (list.length === 0) {
  //           setList([]);
  //         }
  //         initialLoadCompleteRef.current[cacheKey] = true;
  //         setHasMore(false);
  //         console.log('Initial load returned empty, marking as no content');
  //       } else if (emptyPagesCounter.current >= maxEmptyPagesAllowed) {
  //         console.log(
  //           `Reached ${maxEmptyPagesAllowed} empty pages, stopping pagination`,
  //         );
  //         setHasMore(false);
  //       } else {
  //         console.log('Skipping empty page, trying next page');
  //         setPage(prev => prev + 1);

  //         // 延迟一点再尝试下一页
  //         setTimeout(() => {
  //           if (hasMore) {
  //             // 使用原始函数避免循环依赖
  //             fetchMoreData(false);
  //           }
  //         }, 500);
  //       }
  //     } else {
  //       // 收到有效数据，重置空页计数
  //       emptyPagesCounter.current = 0;

  //       if (isInitial) {
  //         setList(newData);
  //         initialLoadCompleteRef.current[cacheKey] = true;
  //         console.log(
  //           `Initial load successful, loaded ${newData.length} items`,
  //         );
  //       } else {
  //         setList(prev => {
  //           const existingIds = new Set(prev.map(post => post.id));
  //           const uniqueNewData = newData.filter(
  //             post => !existingIds.has(post.id),
  //           );
  //           console.log(`Added ${uniqueNewData.length} new items to list`);
  //           return [...prev, ...uniqueNewData];
  //         });

  //         setPage(prev => prev + 1);
  //       }
  //     }

  //     // 成功后清除所有错误
  //     setInitialLoadError(null);
  //     setPaginationError(null);
  //     setError(null);
  //   } catch (err) {
  //     console.error('Error fetching data:', err);

  //     let errorMessage = t('errors.loadFailed', 'Failed to load feed');
  //     if (err instanceof Error) {
  //       if (err.name === 'AbortError') {
  //         errorMessage = t(
  //           'errors.timeout',
  //           'Request timeout. Please try again.',
  //         );
  //       } else {
  //         errorMessage = err.message;
  //       }
  //     }

  //     if (isInitial) {
  //       if (list.length === 0) {
  //         setInitialLoadError(errorMessage);
  //         setError(errorMessage);
  //       }
  //       initialLoadCompleteRef.current[cacheKey] = true;
  //       console.log('Initial load failed:', errorMessage);
  //     } else {
  //       setPaginationError(errorMessage);
  //       console.log('Pagination load failed:', errorMessage);
  //     }
  //   } finally {
  //     setIsLoading(false);
  //     setIsLoadingMore(false);
  //     isLoadingRef.current = false;
  //   }
  // };

  const cancelController = useRef<AbortController | null>(null);

  // 首次加载时pageNo为2，需要标记一下；只要发生tag切换的交互，置为false
  const isInitialRef = useRef(prerenderedPosts);

  const listParamsRef = useRef<{
    tag: string;
    apiTag: number | null;
  }>({
    tag: 'Trending',
    apiTag: null,
  });

  const fetchList = async (isInitial = false) => {
    if (isLoadingRef.current) {
      return;
    }
    setIsLoading(true);
    const tag = listParamsRef.current.tag;
    const apiTag = listParamsRef.current.apiTag;
    let url = `/api/fetchFeed?page=${isInitial ? 1 : paginationRef.current.pageNo}&sortby=${tag}&mainfeedonly=True`;
    if (apiTag) {
      url += `&tag=${apiTag}`;
    }

    const controller = new AbortController();
    // const timeoutId = setTimeout(() => controller.abort(), 10000);
    isLoadingRef.current = true;
    setIsLoadingMore(true);

    cancelController.current = controller;

    // 获取当前语言设置
    const currentLang = localStorage.getItem('lang') || 'en';

    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        'Accept-Language': currentLang,
      },
    }).catch();

    cancelController.current = null;

    try {
      if (!response?.ok) {
        // throw new Error('Failed to fetch list');
        console.error('Failed to fetch list');
        setError(t('errors.loadFailed', 'Failed to load feed'));
        return;
      }

      const posts = await response.json();
      if (!posts || !Array.isArray(posts)) {
        setHasMore(false);
        return;
      }
      // console.log(posts, 'posts');
      if (posts.length < paginationRef.current.pageSize) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      paginationRef.current.pageNo++;
      if (isInitial) {
        setList(posts);
        return;
      }
      setList(prev => {
        const newList = [...prev];
        for (const post of posts) {
          if (!newList.find(p => p.id === post.id)) {
            newList.push(post);
          }
        }
        return newList;
      });
    } catch (e) {
      setError(t('errors.loadFailed', 'Failed to load feed'));
      console.error('Error fetching list:', e);
    } finally {
      isLoadingRef.current = false;
      cancelController.current = null;
      isInitialRef.current = false;
      setIsLoadingMore(false);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    paginationRef.current.pageNo = isInitialRef.current ? 2 : page;
    paginationRef.current.pageSize = 10;
    cancelController.current?.abort('cancel');
    cancelController.current = null;
    isLoadingRef.current = false;
    setIsLoading(false);
    setHasMore(true);
    // console.log('paginationRef', paginationRef.current);
    setError(null);
    setInitialLoadError(null);
    setPaginationError(null);
    listParamsRef.current.tag = selectedTag;
    listParamsRef.current.apiTag = selectedApiTag;
  }, [selectedTag, selectedApiTag]);

  useEffect(() => {
    if (!prerenderedPosts) {
      fetchList(true);
    }
  }, [prerenderedPosts]);

  // 在oc页面， 刷新列表
  useEffect(() => {
    if (
      selectedApiTag &&
      list.length === 0 &&
      !isLoading &&
      !prerenderedPosts &&
      isCharacterFeed &&
      hasMore
    ) {
      const timeoutId = setTimeout(() => {
        fetchList(true);
      }, 100);

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [
    selectedApiTag,
    list.length,
    isLoading,
    prerenderedPosts,
    isCharacterFeed,
    hasMore,
  ]);

  // 监听语言变化事件，重新加载
  useEffect(() => {
    // 使用防抖来避免重复请求
    let timeoutId: NodeJS.Timeout;

    const debouncedFetchPinnedPosts = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        fetchPinnedPosts();
      }, 300); // 300ms 防抖延迟
    };

    // 监听语言变化
    window.addEventListener('languageChanged', debouncedFetchPinnedPosts);
    window.addEventListener('storage', debouncedFetchPinnedPosts);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('languageChanged', debouncedFetchPinnedPosts);
      window.removeEventListener('storage', debouncedFetchPinnedPosts);
    };
  }, []); // 移除依赖项，只在组件挂载时设置监听器

  // const loadMoreCallback = useCallback(() => {
  //   // 创建缓存键，组合排序和筛选条件
  //   const cacheKey = selectedApiTag
  //     ? `${selectedTag}_api_tag_${selectedApiTag}`
  //     : selectedTag;

  //   console.log('loadMoreCallback triggered', {
  //     isLoadingRef: isLoadingRef.current,
  //     isLoading,
  //     isLoadingMore,
  //     hasMore,
  //     initialLoadComplete: initialLoadCompleteRef.current[cacheKey],
  //   });

  //   // 只有在没有正在加载、有更多数据、且初始加载已完成时才触发
  //   if (
  //     !isLoadingRef.current &&
  //     !isLoading &&
  //     !isLoadingMore &&
  //     hasMore &&
  //     initialLoadCompleteRef.current[cacheKey]
  //   ) {
  //     console.log('Triggering fetchMoreData from infinite scroll');
  //     fetchMoreData(false);
  //   } else {
  //     // 如果条件不满足，重置 isFetching 状态
  //     console.log('Conditions not met, resetting isFetching');
  //     // setIsFetching(false);
  //   }
  // }, [isLoading, isLoadingMore, hasMore, selectedTag, selectedApiTag]);

  const [lastElementRef] = useInfiniteScroll(fetchList, {
    rootMargin: '0px 0px 0px 0px',
    threshold: 0.01,
  });

  // 创建memoized版本的fetchMoreData，现在setIsFetching已经可用
  // const memoizedFetchMoreData = useCallback(
  //   async (isInitial = false) => {
  //     console.log('memoizedFetchMoreData', isInitial);
  //     await fetchMoreData(isInitial);
  //     // 加载完成后重置isFetching状态
  //     // setIsFetching(false);
  //   },
  //   [
  //     hasMore,
  //     page,
  //     selectedTag,
  //     selectedApiTag,
  //     t,
  //     list.length,
  //     setList,
  //     setPage,
  //     setHasMore,
  //     setError,
  //     setInitialLoadError,
  //     setPaginationError,
  //     // setIsFetching,
  //   ],
  // );

  // 当加载完成时重置isFetching状态
  // useEffect(() => {
  //   if (!isLoading && !isLoadingMore) {
  //     setIsFetching(false);
  //   }
  // }, [isLoading, isLoadingMore, setIsFetching]);

  // useEffect(() => {
  //   // 创建缓存键，组合排序和筛选条件
  //   const cacheKey = selectedApiTag
  //     ? `${selectedTag}_api_tag_${selectedApiTag}`
  //     : selectedTag;

  //   // 如果已经有数据（从SSR获取），标记为已完成初始加载
  //   if (list.length > 0 && !initialLoadCompleteRef.current[cacheKey]) {
  //     initialLoadCompleteRef.current[cacheKey] = true;
  //     hasAttemptedInitialLoad.current[cacheKey] = true;
  //     setPage(2); // 下次加载第2页
  //     return;
  //   }

  //   // 只有当当前标签没有加载过时才加载
  //   if (!initialLoadCompleteRef.current[cacheKey]) {
  //     console.log('Triggering initial load for cache key:', cacheKey);
  //     memoizedFetchMoreData(true);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [selectedTag, selectedApiTag, memoizedFetchMoreData]);

  // ! HANDLE TAG (排序方式)
  const handleTagChange = (tag: string) => {
    console.log(`切换排序标签到 ${tag}`);
    setSelectedTag(tag);
    // 不清除 selectedApiTag，保持筛选条件
    setList([]); // Clear the list
    setPage(1); // Reset to the first page when the tag changes
    setHasMore(true);
    setError(null);
    setInitialLoadError(null);
    setPaginationError(null);
    isInitialRef.current = false;

    // 清除所有缓存，因为排序方式改变了
    // Object.keys(initialLoadCompleteRef.current).forEach(key => {
    //   delete initialLoadCompleteRef.current[key];
    // });
    // Object.keys(hasAttemptedInitialLoad.current).forEach(key => {
    //   delete hasAttemptedInitialLoad.current[key];
    // });

    setTimeout(() => {
      fetchList(true);
    }, 100);
  };

  // ! HANDLE API TAG (筛选条件)
  const handleApiTagChange = (tagId: number) => {
    console.log(`切换筛选标签到 ${tagId}`);
    if (tagId === selectedApiTag) {
      setSelectedApiTag(null);
    } else {
      setSelectedApiTag(tagId);
    }
    isInitialRef.current = false;
    setList([]); // Clear the list
    setPage(1); // Reset to the first page when the tag changes
    setHasMore(true);
    setError(null);
    setInitialLoadError(null);
    setPaginationError(null);

    // 清除所有缓存，因为筛选条件改变了
    // Object.keys(initialLoadCompleteRef.current).forEach(key => {
    //   delete initialLoadCompleteRef.current[key];
    // });
    // Object.keys(hasAttemptedInitialLoad.current).forEach(key => {
    //   delete hasAttemptedInitialLoad.current[key];
    // });

    // 用setTimeout执行，防止在同一个tick里面abortController与fetchList同时执行
    setTimeout(() => {
      fetchList(true);
    }, 100);
  };

  // ! HANDLE LIKE
  const handleLike = async (id: number) => {
    const updatedList = list.map(item => {
      if (item.id === id) {
        console.log(item);
        const updatedItem = {
          ...item,
          liked: !item.liked,
          votes: item.liked ? item.votes - 1 : item.votes + 1,
        };
        let voteValue = 0;
        if (item.liked) {
          voteValue = -1;
        } else {
          voteValue = 1;
        }

        console.log(
          JSON.stringify({
            postId: item.id,
            parentCommentId: null,
            authUserId: null,
            value: voteValue,
          }),
        );
        // Make API call to update like count on the server
        fetch('/api/vote', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            postId: item.id,
            parentCommentId: null,
            authUserId: null,
            value: voteValue,
          }),
        });
        return updatedItem;
      }
      return item;
    });
    setList(updatedList);
  };

  // ! HANDLE FOLLOW
  const handleFollow = async (id: number) => {
    const updatedList = list.map(item => {
      if (item.id === id) {
        console.log(item);
        const updatedItem = { ...item, followed: !item.followed };
        let followValue = 0;
        if (item.followed) {
          followValue = -1;
        } else {
          followValue = 1;
        }

        console.log(
          JSON.stringify({
            followingUserId: item.authUserId,
            value: followValue,
          }),
        );
        // Make API call to update follow
        fetch('/api/follow', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            followingUserId: item.authUserId,
            value: followValue,
          }),
        });
        return updatedItem;
      }
      return item;
    });
    setList(updatedList);
  };

  // ! HANDLE COMMENT
  const handleCommentChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    setComment(event.target.value);
  };

  let canCall = true;
  const handleComment = async (id: number) => {
    if (!canCall) {
      console.log('Cooldown active. Please wait.');
      return;
    }

    if (!comment.trim()) {
      console.log('Empty comment. Skipping.');
      return;
    }

    canCall = false;

    setTimeout(() => {
      canCall = true;
    }, 3000);

    const updatedList = await Promise.all(
      list.map(async item => {
        if (item.id === id) {
          console.log(`postid ${item.id}`);

          console.log(
            JSON.stringify({
              postId: item.id,
              commentId: null,
              authUserId: null,
              content: comment,
            }),
          );
          // Make API call to update like count on the server
          const response = await fetch('/api/comment', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              postId: item.id,
              content: comment,
              item,
            }),
          });

          const new_comments = await response.json();
          if (!response.ok) {
            throw new Error(new_comments.error || 'Failed to post comment');
          }

          const updatedItem = {
            ...item,
            comments: new_comments,
          };
          return updatedItem;
        }
        return item;
      }),
    );
    setList(updatedList);
    setComment('');
  };

  // ! HANDLE MODAL
  const handleBeforeUnload = () => {
    sessionStorage.setItem('scrollPosition', window.scrollY.toString());
    console.log('beforeunload setting scroll position');
  };

  useEffect(() => {
    const savedScrollPosition = sessionStorage.getItem('scrollPosition');
    console.log('restoring scroll position', savedScrollPosition);
    if (savedScrollPosition) {
      window.scrollTo(0, parseInt(savedScrollPosition));
      sessionStorage.removeItem('scrollPosition');
    }
  }, []);

  const checkIsMobile = () => {
    setIsMobile(window.innerWidth < 768);
  };

  useEffect(() => {
    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  const handleOpen = (id: number, uniqid: string) => {
    try {
      mixpanel.track('click.home.open_post');
    } catch (error) {}
    if (isMobile || forceNavigation) {
      handleBeforeUnload();
      router.push(`/post/${uniqid}`);
    } else {
      setActiveItemId(id);
      onOpen();
    }
  };

  const handleClose = () => {
    if (isMobile) {
    } else {
      setActiveItemId(null);
      onClose();
    }
  };

  // ! HANDLE MODAL SIZE
  const adjustHeight = useCallback(() => {
    const img = document.getElementById('leftElement');
    const rightElement = document.getElementById('rightElement');
    if (img && rightElement) {
      rightElement.style.height = `${img.offsetHeight - 1}px`;
      console.log('adjusting height');
      console.log(`${img.offsetHeight}px`);
    }
  }, []);

  useEffect(() => {
    window.addEventListener('resize', adjustHeight);

    return () => {
      window.removeEventListener('resize', adjustHeight);
    };
  }, [adjustHeight]);

  useEffect(() => {
    if (isOpen) {
      // Use setTimeout to ensure the modal content has rendered
      setTimeout(adjustHeight, 10);
      setTimeout(adjustHeight, 100);
      setTimeout(adjustHeight, 250);
      setTimeout(adjustHeight, 500);
    }
  }, [isOpen, adjustHeight]);

  const renderContent = () => {
    if (isLoading && list.length === 0) {
      return (
        <Masonry
          breakpointCols={{
            default: 6,
            '3000': 5,
            '2400': 4,
            '1200': 3,
            '640': 2,
          }}
          className='flex gap-4 md:mx-2 w-full mx-auto'
          columnClassName='my-masonry-grid_column'>
          {Array(12)
            .fill(0)
            .map((_, index) => (
              <PostCardSkeleton
                key={`initial-skeleton-${index}`}
                index={index}
              />
            ))}
        </Masonry>
      );
    }

    if ((initialLoadError || error) && list.length === 0) {
      return (
        <div className='flex justify-center items-center h-64 text-center'>
          <div>
            <p className='text-danger mb-4'>{initialLoadError || error}</p>
            <Button
              className='bg-primary text-white px-4 py-2 rounded-lg'
              onClick={() => {
                setError(null);
                setInitialLoadError(null);
                setPaginationError(null);
                // const cacheKey = selectedApiTag
                //   ? `${selectedTag}_api_tag_${selectedApiTag}`
                //   : selectedTag;
                // hasAttemptedInitialLoad.current[cacheKey] = false;
                // memoizedFetchMoreData(true);
                fetchList(true);
              }}>
              {t('actions.tryAgain', 'Try Again')}
            </Button>
          </div>
        </div>
      );
    }

    // 创建缓存键，组合排序和筛选条件
    // const cacheKey = selectedApiTag
    //   ? `${selectedTag}_api_tag_${selectedApiTag}`
    //   : selectedTag;

    if (
      list.length === 0 &&
      // hasAttemptedInitialLoad.current[cacheKey] &&
      // initialLoadCompleteRef.current[cacheKey] &&
      !isLoading
    ) {
      return (
        <div className='flex justify-center items-center h-64'>
          <div className='text-center'>
            <p className='text-gray-500 text-lg mb-4'>
              {t('states.noPostsFound', 'No posts found')}
            </p>
            <Button
              className='bg-primary text-white px-4 py-2 rounded-lg'
              onClick={() => {
                // hasAttemptedInitialLoad.current[cacheKey] = false;
                // memoizedFetchMoreData(true);
                fetchList();
              }}>
              {t('actions.refresh', 'Refresh')}
            </Button>
          </div>
        </div>
      );
    }

    return (
      <>
        <Masonry
          breakpointCols={{
            default: 6,
            '3000': 5,
            '2400': 4,
            '1200': 3,
            '640': 2,
          }}
          className={`flex gap-2 md:gap-3 mx-0 md:mx-2 w-full ${
            simple ? 'justify-center' : ''
          }`}
          columnClassName='my-masonry-grid_column'>
          {/* - 只在 Trending 页面显示并且置顶 */}
          {selectedTag === 'Trending' &&
            !isCharacterFeed &&
            !selectedApiTag && (
              <>
                {pinnedPosts.length > 0 &&
                  pinnedPosts.map(item => (
                    <PostCard
                      item={item}
                      handleOpen={handleOpen}
                      handleLike={handleLike}
                      handleFollow={handleFollow}
                      handleComment={handleComment}
                      handleCommentChange={handleCommentChange}
                      comment={comment}
                      isOpen={isOpen && activeItemId === item.id}
                      handleClose={handleClose}
                      useAnchor
                      showMore={item.authUserId === profile?.id}
                    />
                  ))}
                {/* <ActivityPosts index={0} /> */}
                <PWAInstallPost />
              </>
            )}

          {/* 交错显示 Regular Posts 和剩余的 Activity Posts */}
          {filterNSFW(list, selectedApiTag).map((item, index) => (
            <React.Fragment key={`fragment-${item.id}-${item.uniqid}`}>
              {/* 每隔3个post插入一个activity post, 只在 Trending 页面显示 */}
              {shouldInsertActivityPost(index) && (
                <ActivityPosts index={calculateActivityPostIndex(index)} />
              )}

              {/* Regular Post */}
              <PostCard
                key={`post-${item.id}-${item.uniqid}`}
                item={item}
                handleOpen={handleOpen}
                handleLike={handleLike}
                handleFollow={handleFollow}
                handleComment={handleComment}
                handleCommentChange={handleCommentChange}
                comment={comment}
                isOpen={isOpen && activeItemId === item.id}
                handleClose={handleClose}
                useAnchor
                showMore={item.authUserId === profile?.id}
              />
            </React.Fragment>
          ))}

          {/* 加载中的占位图 */}
          {isLoadingMore &&
            Array(4)
              .fill(0)
              .map((_, index) => (
                <PostCardSkeleton
                  key={`loading-skeleton-${index}`}
                  index={index}
                />
              ))}
        </Masonry>

        {/* 分页错误提示 - 显示在内容底部，不影响已有内容 */}
        {paginationError && list.length > 0 && (
          <div className='flex justify-center items-center mt-4 mb-4'>
            <div className='bg-red-50 border border-red-200 rounded-lg p-4 text-center max-w-md'>
              <p className='text-red-600 mb-2 text-sm'>{paginationError}</p>
              <Button
                size='sm'
                className='bg-red-500 text-white'
                onClick={() => {
                  setPaginationError(null);
                  // memoizedFetchMoreData(false);
                  fetchList();
                }}>
                {t('actions.retry', 'Retry')}
              </Button>
            </div>
          </div>
        )}

        {/* 底部加载指示器 */}
        {isLoadingMore && (
          <div className='flex justify-center items-center h-8 w-full mt-2 mb-4'>
            <Spinner size='sm' color='secondary' />
          </div>
        )}

        {!hasMore && list.length > 0 && (
          <div className='flex justify-center mt-8 mb-8'>
            <p className='text-gray-500'>
              {t('states.noMorePosts', 'No more posts to load')}
            </p>
          </div>
        )}

        {/* 无限滚动触发元素  */}
        {hasMore && list.length > 0 && (
          <div
            ref={lastElementRef}
            style={{ height: 20, padding: 0, margin: 0 }}
            className='w-full'
          />
        )}
      </>
    );
  };

  return (
    <div
      className={cn(
        'caffelabs text-foreground w-full flex-grow px-2',
        className,
      )}>
      {!simple && (
        <div className='mb-4'>
          <div className='flex h-8 md:h-10 overflow-hidden hover:overflow-x-auto'>
            <div className='flex gap-2'>
              {tags.map((tag, index) => (
                <Button
                  key={index}
                  onClick={() => handleTagChange(tag.label)}
                  radius='full'
                  size='sm'
                  className={`flex-shrink-0 ${
                    selectedTag === tag.label
                      ? 'text-white bg-gradient-to-tr from-sky-400 to-purple-300 shadow-lg'
                      : 'bg-default-100 text-foreground hover:bg-default-200'
                  }`}>
                  {t(tag.label_key)}
                </Button>
              ))}

              {/* 分隔符 */}
              {/* <div className='w-px h-6 bg-gray-300 mx-2 flex-shrink-0'></div> */}

              {/* 筛选标签 */}
              {isLoadingTags
                ? // 加载中的占位符
                  Array(8)
                    .fill(0)
                    .map((_, index) => (
                      <Skeleton
                        key={`tag-skeleton-${index}`}
                        className='rounded-full w-20 h-8 flex-shrink-0'
                      />
                    ))
                : apiTags.map(tag => (
                    <Button
                      key={tag.id}
                      onClick={() => handleApiTagChange(tag.id)}
                      radius='full'
                      size='sm'
                      className={`flex-shrink-0 capitalize ${
                        selectedApiTag === tag.id
                          ? 'bg-primary text-white'
                          : 'bg-default-100 text-foreground hover:bg-default-200'
                      }`}>
                      #{tag.name}
                    </Button>
                  ))}
            </div>
          </div>
        </div>
      )}

      {/* 内容区域 */}
      {renderContent()}
    </div>
  );
};

export default Feed;
