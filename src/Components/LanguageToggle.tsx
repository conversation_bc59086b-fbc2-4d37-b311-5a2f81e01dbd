import {
  Popover,
  PopoverTrigger,
  PopoverContent,
  Button,
} from '@nextui-org/react';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { IoLanguage } from 'react-icons/io5';
import { changeLocale, getLocale, nextTick } from '../utilities';
import i18n from 'i18n';

import { LANGUAGE_OPTIONS } from '../utilities/languageConstants';

export const languages = LANGUAGE_OPTIONS;

const LanguageToggle = ({ className }: { className?: string }) => {
  const router = useRouter();
  const [currentLanguage, setCurrentLanguage] = useState('en');

  const changeLanguage = (langCode: string) => {
    setCurrentLanguage(langCode);
    changeLocale(langCode);
    router.push(router.pathname, router.asPath.split('?')[0], {
      locale: langCode,
    });

    window.dispatchEvent(
      new CustomEvent('languageChanged', { detail: langCode }),
    );
  };

  useEffect(() => {
    let lang = localStorage.getItem('lang');
    if (!lang) {
      lang = router.locale || 'en';
      changeLocale(lang);
    }
    const supportedLngs = Object.keys(i18n.options.resources || {}) as string[];
    if (router.locale && supportedLngs.includes(router.locale)) {
      setCurrentLanguage(router.locale);
    }
  }, [router.locale]);

  return (
    <Popover placement='bottom'>
      <PopoverTrigger>
        <Button
          isIconOnly={true}
          variant='light'
          className={`h-8 sm:h-10 lg:min-w-unit-16 lg:px-3 ${className}`}
          aria-label='Language'>
          <IoLanguage className='w-4 h-4 sm:w-5 sm:h-5 text-gray-500' />
          {/* <span className="ml-2 text-nowrap">
            {languages.find(lang => lang.code === currentLanguage)?.name || "English"}
          </span> */}
        </Button>
      </PopoverTrigger>
      <PopoverContent className='max-w-[200px] !justify-start p-0'>
        <div className='p-2 max-h-[200px] overflow-y-auto'>
          {languages.map(lang => (
            <Button
              key={lang.code}
              variant='light'
              className='justify-start mb-1 w-full'
              onClick={() => changeLanguage(lang.code)}>
              {lang.name}
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default LanguageToggle;
