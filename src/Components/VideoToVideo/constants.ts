export interface StyleTemplate {
  id: string;
  name: string;
  image: string;
}

export interface StyleTemplateCategory {
  category: string;
  icon: string;
  templates: StyleTemplate[];
}

// Function to get style templates with translations
export const getStyleTemplateCategories = (t: any): StyleTemplateCategory[] => [
  {
    category: t('ui.styleCategories.tStyleTransfer'),
    icon: '🎨',
    templates: [
      {
        id: 'anime',
        name: t('ui.styleTemplates.anime'),
        image: '/images/styles/anime.webp',
      },
      {
        id: 'ghibli-anime',
        name: t('ui.styleTemplates.ghibliAnime'),
        image: '/images/styles/studio_ghibli_anime.webp',
      },
      {
        id: 'comic',
        name: t('ui.styleTemplates.comic'),
        image: '/images/styles/comic.webp',
      },
      {
        id: 'pixar',
        name: t('ui.styleTemplates.pixar'),
        image: '/images/styles/pixar.webp',
      },
      {
        id: 'retro-game',
        name: t('ui.styleTemplates.retroGame'),
        image: '/images/styles/retro_game.webp',
      },
      {
        id: 'mobile-game',
        name: t('ui.styleTemplates.mobileGame'),
        image: '/images/styles/mobile_game.webp',
      },
      {
        id: 'ps-game',
        name: t('ui.styleTemplates.psGame'),
        image: '/images/styles/ps_game.webp',
      },
      {
        id: 'korean-manhwa',
        name: t('ui.styleTemplates.koreanManhwa'),
        image: '/images/styles/korean_manhwa.webp',
      },
      {
        id: 'cartoon',
        name: t('ui.styleTemplates.cartoon'),
        image: '/images/styles/cartoon.webp',
      },
      {
        id: 'manga',
        name: t('ui.styleTemplates.manga'),
        image: '/images/styles/manga.webp',
      },
      {
        id: 'vaporwave',
        name: t('ui.styleTemplates.vaporwave'),
        image: '/images/styles/vaporwave.webp',
      },
      {
        id: 'pencil-sketch',
        name: t('ui.styleTemplates.pencilSketch'),
        image: '/images/styles/pencil_sketch.webp',
      },
      {
        id: 'western-animation',
        name: t('ui.styleTemplates.westernAnimation'),
        image: '/images/styles/western_animation.webp',
      },
      {
        id: 'watercolor',
        name: t('ui.styleTemplates.watercolor'),
        image: '/images/styles/watercolor_v2v.webp',
      },
      {
        id: 'van-gogh',
        name: t('ui.styleTemplates.vanGogh'),
        image: '/images/styles/van_gogh.webp',
      },
      {
        id: 'oil-painting',
        name: t('ui.styleTemplates.oilPainting'),
        image: '/images/styles/oil_painting.webp',
      },
    ],
  },
  {
    category: t('ui.styleCategories.changeMaterial'),
    icon: '💎',
    templates: [
      {
        id: 'clay',
        name: t('ui.styleTemplates.clay'),
        image: '/images/styles/clay.webp',
      },
      {
        id: 'toy-bricks',
        name: t('ui.styleTemplates.toyBricks'),
        image: '/images/styles/toy_bricks.webp',
      },
      {
        id: 'skeleton',
        name: t('ui.styleTemplates.skeleton'),
        image: '/images/styles/skeleton.webp',
      },
      {
        id: 'fire',
        name: t('ui.styleTemplates.fire'),
        image: '/images/styles/fire.webp',
      },
      {
        id: 'muscle',
        name: t('ui.styleTemplates.muscle'),
        image: '/images/styles/muscle.webp',
      },
      {
        id: 'metal',
        name: t('ui.styleTemplates.metal'),
        image: '/images/styles/metal.webp',
      },
      {
        id: 'crystal',
        name: t('ui.styleTemplates.crystal'),
        image: '/images/styles/crystal.webp',
      },
    ],
  },
  {
    category: t('ui.styleCategories.changeEnvironment'),
    icon: '🗺️',
    templates: [
      {
        id: 'kpop-idol',
        name: t('ui.styleTemplates.kpopIdol'),
        image: '/images/styles/kpop_idol.webp',
      },
      {
        id: 'cyberpunk',
        name: t('ui.styleTemplates.cyberpunk'),
        image: '/images/styles/cyberpunk.webp',
      },
      {
        id: 'cloud',
        name: t('ui.styleTemplates.cloud'),
        image: '/images/styles/cloud.webp',
      },
      {
        id: 'mars',
        name: t('ui.styleTemplates.mars'),
        image: '/images/styles/mars.webp',
      },
      {
        id: 'underwater',
        name: t('ui.styleTemplates.underwater'),
        image: '/images/styles/underwater.webp',
      },
      {
        id: 'outer-space',
        name: t('ui.styleTemplates.outerSpace'),
        image: '/images/styles/outer_space.webp',
      },
      {
        id: 'apocalypse',
        name: t('ui.styleTemplates.apocalypse'),
        image: '/images/styles/apocalypse.webp',
      },
      {
        id: 'snow',
        name: t('ui.styleTemplates.snow'),
        image: '/images/styles/snow.webp',
      },
    ],
  },
  {
    category: t('ui.styleCategories.cosplay'),
    icon: '👘',
    templates: [
      {
        id: 'superhero',
        name: t('ui.styleTemplates.superhero'),
        image: '/images/styles/superhero.webp',
      },
      {
        id: 'hogwarts',
        name: t('ui.styleTemplates.hogwarts'),
        image: '/images/styles/hogwarts.webp',
      },
      {
        id: 'barbie',
        name: t('ui.styleTemplates.barbie'),
        image: '/images/styles/barbie.webp',
      },
      {
        id: 'miku',
        name: t('ui.styleTemplates.miku'),
        image: '/images/styles/miku.webp',
      },
      {
        id: 'naruto',
        name: t('ui.styleTemplates.naruto'),
        image: '/images/styles/naruto1.webp',
      },
      {
        id: 'sailor-moon',
        name: t('ui.styleTemplates.sailorMoon'),
        image: '/images/styles/sailor_moon.webp',
      },
      {
        id: 'goku',
        name: t('ui.styleTemplates.goku'),
        image: '/images/styles/goku.webp',
      },
      {
        id: 'sailor-uniform',
        name: t('ui.styleTemplates.sailorUniform'),
        image: '/images/styles/sailor_uniform.webp',
      },
      {
        id: 'princess',
        name: t('ui.styleTemplates.princess'),
        image: '/images/styles/princess.webp',
      },
      {
        id: 'kimono',
        name: t('ui.styleTemplates.kimono'),
        image: '/images/styles/kimono.webp',
      },
      {
        id: 'magical-girl',
        name: t('ui.styleTemplates.magicalGirl'),
        image: '/images/styles/magical-girl.webp',
      },
      {
        id: 'cowboy',
        name: t('ui.styleTemplates.cowboy'),
        image: '/images/styles/cowboy.webp',
      },
    ],
  },
];

export const presetPrompts: Record<string, string> = {
  'retro-game':
    'Pixel art style video. Retro-style video game interface. Game UI elements are dynamically changing. Health bar is decreasing. Map with moving icons. Score counters incrementing.',
  'mobile-game':
    'A dynamic game play screen recording showing dynamic game interface.',
  'ps-game':
    'A dynamic game play screen recording showing dynamic game interface.',
  snow: 'A heavy snowstorm.',
};