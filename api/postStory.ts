// export const dynamic = 'force-dynamic'; // static by default, unless reading the request

import { parse } from 'cookie';
import { decode } from 'next-auth/jwt';
import { createClient } from '@supabase/supabase-js';
import OpenAI from 'openai';
import { waitUntil } from '@vercel/functions';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
);
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

function generate16CharUUID() {
  const chars = 'abcdefghijklmnopqrstuvwxyz';
  let uuid = '';
  for (let i = 0; i < 16; i++) {
    uuid += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return uuid;
}

/**
 * Checks if text content contains NSFW material using GPT
 */
async function checkTextForNSFW(text: string): Promise<boolean> {
  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content:
            'You are a content moderation assistant. Respond with only "true" if the content contains NSFW (Not Safe For Work) material including adult content, explicit language, violence, or other inappropriate content. Otherwise respond with "false".',
        },
        {
          role: 'user',
          content: text,
        },
      ],
      temperature: 0.1,
    });

    const result = response.choices[0].message.content?.trim().toLowerCase();
    return result === 'true';
  } catch (error) {
    console.error('Error checking text for NSFW content:', error);
    return false; // Default to false if the check fails
  }
}

/**
 * Checks if an image URL contains NSFW content using GPT
 * Only processes supported image formats (.webp, .png, .jpeg, .jpg)
 */
async function checkImageForNSFW(imageUrl: string): Promise<boolean> {
  // Check if the image has a supported extension
  const supportedExtensions = ['.webp', '.png', '.jpeg', '.jpg'];
  const hasValidExtension = supportedExtensions.some(ext =>
    imageUrl.toLowerCase().endsWith(ext),
  );

  if (!hasValidExtension) {
    return false; // Skip checking non-supported image formats
  }

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4.1-mini',
      messages: [
        {
          role: 'system',
          content:
            'You are a content moderation assistant. Respond with only "true" if the image contains NSFW (Not Safe For Work) material including adult content, nudity, violence, or other inappropriate content. Otherwise respond with "false".',
        },
        {
          role: 'user',
          content: [
            { type: 'text', text: 'Is this image NSFW?' },
            { type: 'image_url', image_url: { url: imageUrl } },
          ],
        },
      ],
      temperature: 0.1,
      max_tokens: 10,
    });

    const result = response.choices[0].message.content?.trim().toLowerCase();
    return result === 'true';
  } catch (error) {
    console.error('Error checking image for NSFW content:', error);
    return false; // Default to false if the check fails
  }
}

interface Tag {
  id: number;
  name: string;
}

const hasPrivateTag = (tags: Tag[]) => !!tags.find(tag => tag.name === 'NSFW');

export async function POST(request: Request) {
  try {
    const {
      title,
      description,
      images,
      generations,
      tags,
      hide_main_feed,
      media_type,
      new_tags = [],
      is_pinned = false,
      translate_post = false,
    } = await request.json();

    // Parse cookies from the request headers
    const cookies = parse(request.headers.get('cookie') || '');
    const sessionToken = cookies['next-auth.session-token'];
    if (!sessionToken) {
      return new Response(
        JSON.stringify({ error: 'Log in to generate images' }),
        { status: 401 },
      );
    }
    const token = await decode({
      token: sessionToken,
      secret: process.env.NEXTAUTH_SECRET!,
    });
    if (!token) {
      return new Response(JSON.stringify({ error: 'Invalid login status' }), {
        status: 401,
      });
    }
    const userId = token.id;
    const postId = generate16CharUUID();

    // const imageUrls = [];
    // for (const image of images) {
    //     const blob = await fetch(image).then(res => res.blob());
    //     let imagePath = `app_media/${uuidv4()}.webp`;
    //     const result = await supabase.storage.from("husbando-land").upload(imagePath, blob)
    //     if (!result.error) {
    //         let imageUrl = supabase.storage.from("husbando-land").getPublicUrl(imagePath).data.publicUrl;
    //         imageUrls.push(imageUrl);
    //     }
    // }
    // console.log(imageUrls)
    // Check for NSFW content in title and description
    const isTextNSFW = await checkTextForNSFW(
      `Title: ${title || ''} \n ${description ? `Description: ${description}` : ''}`,
    );

    // // Check images for NSFW content
    const imageNSFWPromises = images.map((imageUrl: string) =>
      checkImageForNSFW(imageUrl),
    );
    const imageNSFWResults = await Promise.all(imageNSFWPromises);
    const isAnyImageNSFW = imageNSFWResults.some(result => result);

    // // If any content is NSFW, reject the post
    if (isTextNSFW || isAnyImageNSFW) {
      const nsfwTag = new_tags.find(tag => tag.name === 'NSFW');
      if (!nsfwTag) {
        new_tags.push({
          id: 2,
          name: 'NSFW',
        });
      }
    }

    // If the post is a video, add Animation tag
    if (media_type === 'video') {
      const animationTag = new_tags.find(tag => tag.name === 'Animation');
      if (!animationTag) {
        new_tags.push({
          id: -1, // Will be resolved when creating the tag
          name: 'Animation',
        });
      }
    }
    const isPrivate = hasPrivateTag(new_tags);
    const { data: postData, error: postError } = await supabase
      .from('AppPosts')
      .insert([
        {
          title,
          content: description,
          media: images,
          generations,
          authUserId: userId,
          uniqid: postId,
          tags,
          hide_main_feed,
          media_type,
          is_private: isPrivate,
        },
      ])
      .select('id')
      .single();
    if (postError || !postData) {
      throw postError || new Error('Failed to post');
    }

    // Handle pin functionality if requested and user is official account
    const OFFICIAL_ACCOUNT_ID = process.env.OFFICIAL_ACCOUNT_ID || '';
    if (is_pinned && userId === OFFICIAL_ACCOUNT_ID) {
      try {
        const { error: pinError } = await supabase.from('PinAppPost').insert([
          {
            post_id: postData.id,
            pinned_by: userId,
            is_active: true,
          },
        ]);

        if (pinError) {
          console.error('Error pinning post during creation:', pinError);
          // Don't fail the entire post creation if pinning fails
        }
      } catch (pinError) {
        console.error('Error handling pin during post creation:', pinError);
        // Don't fail the entire post creation if pinning fails
      }
    }

    // Handle translation if requested and user is official account
    if (translate_post && userId === OFFICIAL_ACCOUNT_ID) {
      try {
        // Import translation function from translatePost.ts
        const { GoogleGenerativeAI } = await import('@google/generative-ai');

        const gemini_key =
          process.env.GEMINI_API_KEY
        if (!gemini_key) {
          throw new Error('Gemini API key is not set');
        }
        const genAI = new GoogleGenerativeAI(gemini_key);
        const model = genAI.getGenerativeModel({
          model: 'gemini-2.0-flash',
          generationConfig: {
            temperature: 1,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 8192,
          },
        });

        const {
          LANGUAGE_TO_ENGLISH_NAME: langMap,
          SUPPORTED_LANGUAGES: targetLanguages,
        } = await import('./_utils/index.js');

        async function translateText(
          text: string,
          targetLang: string,
        ): Promise<string> {
          try {
            const languageName = langMap[targetLang as keyof typeof langMap];
            if (!languageName) {
              throw new Error(`Unsupported language: ${targetLang}`);
            }

            const prompt = `将以下文本翻译为${languageName}，保持原文的意思和风格。只返回翻译结果，不要添加任何解释或注释：

原文：
${text}

翻译：`;

            const result = await model.generateContent(prompt);
            const response = await result.response;
            return response.text().trim();
          } catch (error) {
            console.error(`Translation error for ${targetLang}:`, error);
            throw error;
          }
        }

        // 执行翻译
        const translationPromises = targetLanguages.map(async lang => {
          try {
            const [translatedTitle, translatedContent] = await Promise.all([
              translateText(title, lang),
              description
                ? translateText(description, lang)
                : Promise.resolve(''),
            ]);

            return {
              post_id: postData.id,
              language: lang,
              title: translatedTitle,
              content: translatedContent,
            };
          } catch (error) {
            console.error(`Translation failed for ${lang}:`, error);
            return null;
          }
        });

        const translationResults = await Promise.all(translationPromises);
        const validTranslations = translationResults.filter(
          result => result !== null,
        );

        if (validTranslations.length > 0) {
          const { error: translationError } = await supabase
            .from('PostTranslations')
            .insert(validTranslations);

          if (translationError) {
            console.error('Error saving translations:', translationError);
            // Don't fail the entire post creation if translation fails
          }
        }
      } catch (translationError) {
        console.error(
          'Error handling translation during post creation:',
          translationError,
        );
        // Don't fail the entire post creation if translation fails
      }
    }

    let message = 'Story posted!';
    // Award credits for first post of the day
    const { data: dateData } = await supabase
      .from('User')
      .select('date_post, credit')
      .eq('id', userId)
      .single();
    // console.log(dateData);
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];
    if (dateData?.date_post !== todayString) {
      await supabase
        .from('User')
        .update({ date_post: new Date(), credit: dateData?.credit + 50 })
        .eq('id', userId);
      message =
        'Story posted! You have been awarded 50 credits for your first post of the day!🎉';
    }
    if (new_tags && new_tags.length > 0) {
      const postDbId = postData.id;

      const processNewTags = async () => {
        for (const tag of new_tags) {
          let tagId = tag.id;

          if (tagId < 0) {
            // This is a new tag, need to create it first
            try {
              const { data: newTagData, error: newTagError } = await supabase
                .from('tags')
                .insert([{ name: tag.name, popularity: 0 }])
                .select('id')
                .single();

              if (newTagError) {
                // Check if it's a unique constraint violation
                if (newTagError.code === '23505') {
                  // Tag already exists, get its ID
                  const { data: existingTag } = await supabase
                    .from('tags')
                    .select('id')
                    .eq('name', tag.name)
                    .single();
                  tagId = existingTag?.id;
                } else {
                  console.error('Error creating tag:', newTagError);
                  continue;
                }
              } else {
                tagId = newTagData.id;
              }
            } catch (error) {
              console.error('Error processing new tag:', error);
              continue;
            }
          }

          if (tagId >= 0) {
            // Insert into post_tags
            const { error: postTagError } = await supabase
              .from('post_tags')
              .insert([{ tag_id: tagId, post_id: postDbId }]);

            if (postTagError) {
              console.error('Error linking tag to post:', postTagError);
            }
          }
        }
      };

      // Process tags asynchronously using waitUntil if available
      waitUntil(processNewTags());
    }

    return new Response(JSON.stringify({ message, postId }), { status: 200 });
  } catch (error) {
    console.error('Error posting:', error);
    return new Response(JSON.stringify({ error: 'Failed to post' }), {
      status: 500,
    });
  }
}
