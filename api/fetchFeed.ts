import { createClient } from '@supabase/supabase-js';
import { PostgrestFilterBuilder } from '@supabase/postgrest-js';
import { parse } from 'cookie';
import { decode } from 'next-auth/jwt';
import {
  getTargetLanguage,
  mapTranslations,
  TranslationsMap,
} from './_utils/index.js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Simple in-memory cache for high-traffic queries
const queryCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 30 * 1000; // 30 seconds cache
const MAX_CACHE_SIZE = 1000;

// Request rate limiting per IP
const requestCounts = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = 100; // requests per minute
const RATE_WINDOW = 60 * 1000; // 1 minute

function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  return forwarded?.split(',')[0] || realIP || 'unknown';
}

function isRateLimited(ip: string): boolean {
  const now = Date.now();
  const record = requestCounts.get(ip);

  if (!record || now > record.resetTime) {
    requestCounts.set(ip, { count: 1, resetTime: now + RATE_WINDOW });
    return false;
  }

  if (record.count >= RATE_LIMIT) {
    return true;
  }

  record.count++;
  return false;
}

function getCacheKey(params: any): string {
  return JSON.stringify(params);
}

function getFromCache(key: string): any | null {
  const cached = queryCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  queryCache.delete(key);
  return null;
}

function setCache(key: string, data: any): void {
  // Clean old entries if cache is too large
  if (queryCache.size >= MAX_CACHE_SIZE) {
    const oldestKey = queryCache.keys().next().value;
    if (oldestKey) {
      queryCache.delete(oldestKey);
    }
  }
  queryCache.set(key, { data, timestamp: Date.now() });
}

type MediaType = 'image' | 'video' | 'text';

// Define the types for the post and user data
interface Post {
  id: number;
  uniqid: string;
  user_uniqid: string;
  authUserId: string;
  created_at: string;
  views: number;
  rizz: number;
  title: string;
  content: string;
  media: string[];
  votes: number;
  liked: boolean;
  comments: Comment[];
  followed: boolean;
  generations: string[];
  media_type: MediaType;
  post_tags: { tags: { id: number; name: string } }[];
}

interface Comment {
  postid: number;
  content: string;
  votes: number;
  user_name: string;
  image: string;
  created_at: string;
}

interface Generation {
  post_id: number;
  image_id: number;
  image_created_at: Date;
  prompt: string;
  model: string;
  url_path: string;
}

interface User {
  id: string;
  user_name: string;
  image: string;
  user_uniqid: string;
  is_cpp?: boolean;
  plan?: string;
}

interface VoteCount {
  postid: number;
  count: number;
}

interface RizzCount {
  postid: number;
  rizz: number;
}

async function updateRefCode(
  authUserId: string,
  newRefCode: string,
): Promise<void> {
  supabase
    .rpc('update_ref_code', {
      authuserid: authUserId,
      new_ref_code: newRefCode,
    })
    .then(({ error }) => {
      if (error) {
        console.error('Error updating ref_code:', error);
      } else {
        console.log('Ref_code update function called successfully');
      }
    });
}

// Function to process posts with all the necessary data from Supabase
async function postProcessPosts(
  postsData: Post[],
  auth_user_id: string,
  clientLanguage?: string,
): Promise<any[]> {
  if (!postsData || postsData.length === 0) {
    return [];
  }

  // ! FETCH USER DATA (always needed)
  const userIds = postsData.map(post => post.authUserId);
  const fetchUserData = supabase
    .from('User')
    .select('id, user_name, image, user_uniqid, is_cpp')
    .in('id', userIds)
    .returns<User[]>();

  // ! FETCH USER SUBSCRIPTION DATA
  // const tableName = process.env.MODE === 'development' ? 'Subscriptions_test' : 'Subscriptions';
  const fetchSubscriptionData = supabase
    .from('Subscriptions')
    .select('user_id, plan')
    .in('user_id', userIds)
    .gt('expires', Math.floor(Date.now() / 1000))
    .order('expires', { ascending: false });

  const postIds = postsData.map(post => post.id);

  // Simplified mode has been completely disabled - always fetch full data

  // ! FETCH LIKE DATA
  const fetchVotesData = supabase
    .from('AppVotes')
    .select('authUserId, postId')
    .in('postId', postIds)
    .in('authUserId', [auth_user_id])
    .returns<{ authUserId: string; postId: number }[]>();

  // ! FETCH FOLLOW DATA
  const fetchFollowData = supabase
    .from('AppFollows')
    .select('follower, following')
    .in('following', userIds)
    .in('follower', [auth_user_id])
    .returns<{ follower: string; following: string }[]>();

  // ! FETCH COMMENTS
  const fetchCommentsData = supabase
    .rpc('get_comments_by_post', { post_ids: postIds })
    .returns<Comment[]>();

  const isOfficialAccount =
    process.env.OFFICIAL_ACCOUNT_ID &&
    auth_user_id &&
    auth_user_id === process.env.OFFICIAL_ACCOUNT_ID;

  let fetchPinData;
  // ! FETCH PIN STATUS if user is official account
  if (isOfficialAccount) {
    fetchPinData = supabase
      .from('PinAppPost')
      .select('post_id')
      .in('post_id', postIds)
      .eq('is_active', true);
  } else {
    // Provide a default promise that resolves to empty data
    fetchPinData = Promise.resolve({ data: null, error: null });
  }

  // ! FETCH IMAGE GENERATIONS
  // const fetchGeneratinsData = supabase
  //   .rpc('get_image_generations_by_post', { post_ids: postIds })
  //   .returns<Generation[]>()
  const fetchGeneratinsData = Promise.resolve<{
    data: Generation[];
    error: any;
  }>({
    data: [],
    error: null,
  });

  // ! FETCH TRANSLATIONS DATA (for single posts and non-English list requests)
  let fetchTranslationsData;
  if (clientLanguage) {
    const targetLanguage = getTargetLanguage(clientLanguage);
    fetchTranslationsData = supabase
      .from('PostTranslations')
      .select('post_id, language, title, content')
      .in('post_id', postIds)
      .eq('language', targetLanguage)
      .not('title', 'is', null)
      .not('content', 'is', null)
      .neq('title', '')
      .neq('content', '');
  } else {
    // Provide a default promise that resolves to empty data
    fetchTranslationsData = Promise.resolve({ data: null, error: null });
  }

  // Execute read-only requests concurrently first
  const [
    { data: usersData, error: usersError },
    { data: subscriptionsData, error: subscriptionsError },
    { data: votesData, error: votesError },
    { data: followsData, error: followsError },
    { data: commentsData, error: commentsError },
    { data: pinData, error: pinError },
    { data: generationsData, error: generationsError },
    { data: translationsData, error: translationsError },
  ] = await Promise.all([
    fetchUserData,
    fetchSubscriptionData,
    fetchVotesData,
    fetchFollowData,
    fetchCommentsData,
    fetchPinData,
    fetchGeneratinsData,
    fetchTranslationsData,
  ]);

  // Debug translation data
  if (clientLanguage) {
    console.log(`[fetchFeed] Translation data received:`, {
      count: translationsData?.length || 0,
      data: translationsData,
      error: translationsError,
    });
  }

  // Execute write operations sequentially to avoid deadlocks
  // ! FETCH VOTES COUNT DATA (with retry logic)
  let votesCountData, votesCountError;
  let rizzCountData, rizzError;

  try {
    // Add jitter to reduce thundering herd effect
    const jitter = Math.random() * 100;
    await new Promise(resolve => setTimeout(resolve, jitter));

    // Execute vote count update first with timeout
    const votesPromise = supabase
      .rpc('count_and_update_votes_for_posts', { post_ids: postIds })
      .returns<VoteCount[]>();

    const votesResult = (await Promise.race([
      votesPromise,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('RPC timeout')), 10000),
      ),
    ])) as any;

    votesCountData = votesResult.data;
    votesCountError = votesResult.error;

    // Only update rizz if votes update succeeded and we have data
    if (!votesCountError && postIds.length > 0) {
      // Add another small delay to reduce lock contention
      await new Promise(resolve =>
        setTimeout(resolve, 50 + Math.random() * 50),
      );

      const rizzPromise = supabase
        .rpc('update_trending_score', { post_ids: postIds })
        .returns<RizzCount[]>();

      const rizzResult = (await Promise.race([
        rizzPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('RPC timeout')), 10000),
        ),
      ])) as any;

      rizzCountData = rizzResult.data;
      rizzError = rizzResult.error;
    }
  } catch (error) {
    console.error('Error in sequential RPC execution:', error);

    // Enhanced fallback: try to get current vote counts without update
    if (!votesCountData) {
      try {
        const fallbackVotes = await supabase
          .from('AppVotes')
          .select('postId')
          .in('postId', postIds);

        if (fallbackVotes.data) {
          const voteCountMap = fallbackVotes.data.reduce(
            (acc, vote) => {
              acc[vote.postId] = (acc[vote.postId] || 0) + 1;
              return acc;
            },
            {} as { [key: number]: number },
          );

          votesCountData = Object.entries(voteCountMap).map(
            ([postid, count]) => ({
              postid: parseInt(postid),
              count,
            }),
          ) as VoteCount[];
        }
      } catch (fallbackError) {
        console.error('Fallback vote count failed:', fallbackError);
        // Use empty array as last resort
        votesCountData = [];
      }
    }

    // For rizz, use existing values from posts if RPC failed
    if (!rizzCountData) {
      rizzCountData = postsData.map(post => ({
        postid: post.id,
        rizz: post.rizz || 0,
      }));
    }
  }

  // Handle errors if any
  if (
    usersError ||
    votesError ||
    votesCountError ||
    rizzError ||
    commentsError ||
    followsError ||
    generationsError ||
    translationsError
  ) {
    console.error(
      'Error processing posts:',
      usersError ||
        votesError ||
        votesCountError ||
        rizzError ||
        commentsError ||
        followsError ||
        generationsError ||
        translationsError,
    );
    return [];
  }

  // ! MAP DATA TO RESPONSE
  // Map votes to posts
  const votesMap =
    votesData?.reduce<{ [key: number]: { [key: string]: boolean } }>(
      (acc, vote) => {
        if (!acc[vote.postId]) {
          acc[vote.postId] = {};
        }
        acc[vote.postId][vote.authUserId] = true;
        return acc;
      },
      {},
    ) || {};

  // Map vote count to posts
  const voteCountMap =
    votesCountData?.reduce(
      (acc: { [key: number]: number }, voteCount: VoteCount) => {
        acc[voteCount.postid] = voteCount.count;
        return acc;
      },
      {} as { [key: number]: number },
    ) || {};

  // Map follow to posts
  const followMap =
    followsData?.reduce<{ [key: string]: boolean }>((acc, follow) => {
      acc[follow.following] = true;
      return acc;
    }, {}) || {};

  // Map subscriptions to users
  const subscriptionsMap: { [key: string]: string } =
    subscriptionsData?.reduce<{ [key: string]: string }>(
      (acc: { [key: string]: string }, sub: any) => {
        acc[sub.user_id] = sub.plan;
        return acc;
      },
      {},
    ) || {};

  // Map user names to posts
  const usersMap: {
    [key: string]: {
      user_name: string;
      image: string;
      user_uniqid: string;
      plan: string;
    };
  } =
    usersData?.reduce<{
      [key: string]: {
        user_name: string;
        image: string;
        user_uniqid: string;
        plan: string;
      };
    }>((acc, user: User) => {
      const userPlan = user.is_cpp
        ? 'CPP'
        : subscriptionsMap[user.id] || 'Free';

      acc[user.id] = {
        user_name: user.user_name,
        image: user.image,
        user_uniqid: user.user_uniqid,
        plan: userPlan,
      };
      return acc;
    }, {}) || {};

  // MAP COMMENTS TO POSTS
  const commentsMap =
    (Array.isArray(commentsData) ? commentsData : [])?.reduce<{
      [key: number]: Comment[];
    }>((acc, comment) => {
      if (!acc[comment.postid]) {
        acc[comment.postid] = [];
      }
      acc[comment.postid].push(comment);
      return acc;
    }, {}) || {};

  // MAP GENERATIONS TO POSTS
  const generationsMap =
    (Array.isArray(generationsData) ? generationsData : [])?.reduce<{
      [key: number]: Generation[];
    }>((acc, generation) => {
      if (!acc[generation.post_id]) {
        acc[generation.post_id] = [];
      }
      acc[generation.post_id].push(generation);
      return acc;
    }, {}) || {};

  // MAP PIN STATUS TO POSTS
  let pinMap;
  if (isOfficialAccount) {
    pinMap =
      pinData?.reduce(
        (acc, pin) => {
          acc[pin.post_id] = true;
          return acc;
        },
        {} as { [key: number]: boolean },
      ) || {};
  }

  // MAP TRANSLATIONS TO POSTS
  const translationsMap: TranslationsMap = mapTranslations(translationsData);

  return postsData.map(post => ({
    ...post,
    user_name: usersMap[post.authUserId]?.user_name || '',
    image: usersMap[post.authUserId]?.image || null,
    user_uniqid: usersMap[post.authUserId]?.user_uniqid || '',
    user_plan: usersMap[post.authUserId]?.plan || 'Free',
    post_tags: post?.post_tags?.map(tag => tag.tags) || [],
    liked: !!votesMap[post.id]?.[auth_user_id],
    followed: followMap[post.authUserId] || false,
    votes: voteCountMap[post.id] || 0,
    comments: commentsMap[post.id] || [],
    generations: generationsMap[post.id] || [],
    isPinned: isOfficialAccount && pinMap ? !!pinMap[post.id] : false,
    translations: translationsMap[post.id] || {},
  }));
}

export async function GET(request: Request) {
  const startTime = Date.now();
  // TODO: 如果后期这种标签增多可以在tags表中加一列做标识，而不是硬编码
  // nsfw tag
  const privateTagIds = [2];

  // Rate limiting check
  const clientIP = getClientIP(request);
  if (isRateLimited(clientIP)) {
    return new Response(JSON.stringify({ error: 'Rate limit exceeded' }), {
      status: 429,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // ! FETCH AUTHID
  const cookies = parse(request.headers.get('cookie') || '');
  const sessionToken = cookies['next-auth.session-token'];
  let auth_user_id = '' as string;
  if (!sessionToken) {
    // return new Response(JSON.stringify({ error: 'Log in to generate images' }), { status: 401 });
    auth_user_id = '' as string;
  } else {
    try {
      const token = await decode({
        token: sessionToken,
        secret: process.env.NEXTAUTH_SECRET!,
      });
      if (!token) {
        auth_user_id = '' as string;
      } else {
        auth_user_id = token.id as string;
      }
    } catch (error) {
      console.error('Error fetching user:', error);
      auth_user_id = '' as string;
    }
  }

  const refCode = cookies['ref'];
  if (refCode) {
    console.log('found ref code:', refCode);
    updateRefCode(auth_user_id, refCode);
  }

  console.log('fetching feed:', auth_user_id);

  // ! PAGINATION
  const imagesPerPage = 15;
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  const limit = imagesPerPage;
  const offset = (page - 1) * limit;
  const sortby = url.searchParams.get('sortby') || 'Newest';
  const useronly = url.searchParams.get('useronly') || 'None';
  const postonly = url.searchParams.get('postonly') || 'None';
  const mainfeedonly = url.searchParams.get('mainfeedonly') || 'None';
  const tagsonly = url.searchParams.get('tagsonly') || 'None';
  const mediaType = url.searchParams.get('mediaType') || 'None';
  const tagParam = url.searchParams.get('tag') || '';
  const tag = tagParam ? parseInt(tagParam) : null;

  // Create cache key for this request (exclude auth_user_id for public feeds)
  const cacheParams = {
    page,
    sortby,
    useronly,
    postonly,
    mainfeedonly,
    tagsonly,
    mediaType,
    tag,
    // Only include auth_user_id in cache key if it affects the result
    ...(useronly === 'True' || sortby === 'following' ? { auth_user_id } : {}),
  };
  const cacheKey = getCacheKey(cacheParams);

  // Check cache for public feeds (not user-specific data)
  if (
    useronly === 'None' &&
    postonly === 'None' &&
    sortby !== 'Following' &&
    !auth_user_id
  ) {
    const cachedResult = getFromCache(cacheKey);
    if (cachedResult) {
      console.log('Returning cached result');
      return new Response(JSON.stringify(cachedResult), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  // console.log(sortby);

  // COMPOSE FETCH QUERY AND FETCH POST DATA
  // ! FILTER DB
  // Select only necessary fields to reduce data transfer
  const selectFields =
    'id, uniqid, authUserId, created_at, views, rizz, title, content, media, votes, tags, media_type, post_tags(tags(id,name))';
  let feedQuery: PostgrestFilterBuilder<any, any, any[], 'AppPosts', unknown> =
    supabase.from('AppPosts').select(selectFields);

  // Handle following filter - get following users first
  let followingUserIds: string[] = [];
  if (sortby === 'Following') {
    if (!auth_user_id) {
      return new Response(JSON.stringify([]), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { data: followingData, error: followingError } = await supabase
      .from('AppFollows')
      .select('following')
      .eq('follower', auth_user_id);

    if (followingError) {
      console.error('Error fetching following data:', followingError);
      return new Response(JSON.stringify({ error: followingError.message }), {
        status: 500,
      });
    }

    if (!followingData || followingData.length === 0) {
      // User is not following anyone, return empty array
      return new Response(JSON.stringify([]), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Extract following user IDs
    followingUserIds = followingData.map(follow => follow.following);
  }
  // Apply sorting based on sortby parameter (skip if using tag parameter since sorting is done in step 1)
  if (!tag) {
    if (sortby === 'Trending') {
      feedQuery = feedQuery
        .order('rizz', { ascending: false })
        .order('created_at', { ascending: false });
    } else if (sortby === 'Most Likes') {
      feedQuery = feedQuery
        .order('votes', { ascending: false })
        .order('created_at', { ascending: false });
    } else {
      // Default: Newest
      feedQuery = feedQuery.order('created_at', { ascending: false });
    }
  }

  if (mainfeedonly !== 'None') {
    // Only show main feed (ie, hide_main_feed != true)
    feedQuery = feedQuery.not('hide_main_feed', 'is', true);
  }
  if (useronly !== 'None') {
    // ! HANDLE AUTHENTICATED PROFILE FEED
    if (useronly === 'True') {
      feedQuery = feedQuery.eq('authUserId', auth_user_id);
    } else {
      // ! HANDLE PUBLIC PROFILE PAGE FEED
      const { data: queryUserId, error: queryUserError } = await supabase
        .from('User')
        .select('id')
        .eq('user_uniqid', useronly)
        .single();

      if (queryUserError) {
        return new Response(JSON.stringify({ error: queryUserError.message }), {
          status: 400,
        });
      }

      console.log('matched', useronly, 'to', queryUserId.id);

      feedQuery = feedQuery.eq('authUserId', queryUserId.id);
    }
  }

  if (postonly !== 'None') {
    feedQuery = feedQuery.eq('uniqid', postonly);
  }

  if (tagsonly !== 'None') {
    const tags = tagsonly.split(',');
    // console.log('tagsonly', tags);
    feedQuery = feedQuery.overlaps('tags', tags);

    // 为 tagsonly 查询添加索引提示和优化
    // 限制查询范围以提高性能
    if (tags.includes('Meme') || tags.includes('Comic')) {
      // 对于漫画/表情包查询，限制时间范围以提高性能
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      feedQuery = feedQuery.gte('created_at', thirtyDaysAgo.toISOString());

      // 添加额外的过滤条件以提高查询效率
      feedQuery = feedQuery.not('media', 'is', null); // 确保有媒体内容
      feedQuery = feedQuery.neq('media_type', 'text'); // 排除纯文本帖子

      // 限制结果数量，减少后续处理的数据量
      feedQuery = feedQuery.limit(Math.min(limit * 2, 50)); // 最多50条
    }
  }

  // Handle tag parameter - query through post_ids_by_tag view
  if (tag) {
    // Step 1: Get post IDs from post_ids_by_tag view with proper sorting and pagination
    let tagQuery = supabase
      .from('post_ids_by_tag')
      .select(
        'post_id, rizz, created_at, votes, "authUserId", hide_main_feed, is_private',
      )
      .eq('tag_id', tag);

    // Apply the same filters to the tag query
    if (useronly !== 'None') {
      if (useronly === 'True') {
        tagQuery = tagQuery.eq('"authUserId"', auth_user_id);
      } else {
        // For public profile, we need to get the user ID first
        const { data: queryUserId, error: queryUserError } = await supabase
          .from('User')
          .select('id')
          .eq('user_uniqid', useronly)
          .single();

        if (queryUserError) {
          return new Response(
            JSON.stringify({ error: queryUserError.message }),
            {
              status: 400,
            },
          );
        }
        tagQuery = tagQuery.eq('"authUserId"', queryUserId.id);
      }
    }
    // Apply sorting based on sortby parameter
    if (sortby === 'Trending') {
      tagQuery = tagQuery
        .order('rizz', { ascending: false })
        .order('created_at', { ascending: false });
    } else if (sortby === 'Most Likes') {
      tagQuery = tagQuery
        .order('votes', { ascending: false })
        .order('created_at', { ascending: false });
    } else if (sortby === 'Following') {
      // For Following, we need to filter by following users first
      if (!auth_user_id || followingUserIds.length === 0) {
        // No following users, return empty array
        return new Response(JSON.stringify([]), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      tagQuery = tagQuery.in('"authUserId"', followingUserIds);
      tagQuery = tagQuery.order('created_at', { ascending: false });
    } else {
      // Default: Newest
      tagQuery = tagQuery.order('created_at', { ascending: false });
    }
    if (mainfeedonly !== 'None') {
      tagQuery = tagQuery.not('hide_main_feed', 'is', true);
    }

    tagQuery = tagQuery.eq('is_private', privateTagIds.includes(tag));
    // Apply pagination
    tagQuery = tagQuery.range(offset, offset + limit - 1);

    const { data: tagData, error: tagError } = await tagQuery;

    if (tagError) {
      console.error('Error fetching posts with tag:', tagError);
      return new Response(JSON.stringify({ error: tagError.message }), {
        status: 500,
      });
    }

    if (!tagData || tagData.length === 0) {
      // No posts found with this tag, return empty array
      return new Response(JSON.stringify([]), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Extract post IDs from the tag query result
    const postIds = tagData.map(item => item.post_id);

    // Step 2: Query AppPosts with the filtered post IDs and get all tags
    feedQuery = feedQuery.in('id', postIds);

    // Apply filters that are not available in the view
    // if (mainfeedonly !== 'None') {
    //   feedQuery = feedQuery.not('hide_main_feed', 'is', true);
    // }
    if (postonly !== 'None') {
      feedQuery = feedQuery.eq('uniqid', postonly);
    }
    if (tagsonly !== 'None') {
      const tags = tagsonly.split(',');
      feedQuery = feedQuery.overlaps('tags', tags);
    }
    if (mediaType === 'video') {
      feedQuery = feedQuery.eq('media_type', 'video');
    }
  } else if (postonly === 'None') {
    feedQuery = feedQuery.eq('is_private', false);
  }

  // Apply following filter after other filters (skip if using tag parameter since it's handled in step 1)
  if (sortby === 'Following' && !tag) {
    feedQuery = feedQuery.in('authUserId', followingUserIds);
  }
  // console.log('postid', postonly);

  // Filter by media type if specified (skip if using tag parameter since it's handled in step 1)
  if (mediaType === 'video' && !tag) {
    // Use media_type field to filter videos
    feedQuery = feedQuery.eq('media_type', 'video');
  }

  // Apply pagination at the end (skip if using tag parameter since pagination is done in step 1)
  if (!tag) {
    feedQuery = feedQuery.range(offset, offset + limit - 1);
  }

  const queryStartTime = Date.now();
  const { data: postsData, error: postsError } =
    await feedQuery.returns<Post[]>();
  const queryTime = Date.now() - queryStartTime;

  if (postsError) {
    console.error('Database query error:', postsError);
    return new Response(JSON.stringify({ error: postsError.message }), {
      status: 500,
    });
  }

  if (postsData.length === 0) {
    console.warn('No data found in AppPosts table.');
    const totalTime = Date.now() - startTime;
    console.log(
      `Query performance: ${queryTime}ms (query), ${totalTime}ms (total)`,
    );
    return new Response(JSON.stringify([]), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // Get language from Accept-Language header
  const acceptLanguage = request.headers.get('accept-language') || 'en';
  const clientLanguage = acceptLanguage.split(',')[0].split('-')[0];

  // Check if we should fetch translations
  // - Always for single post requests (postonly !== 'None')
  // - For list requests, only if language is not English (to support profile pages)
  const shouldFetchTranslations =
    postonly !== 'None' || clientLanguage !== 'en';

  // Process posts using the shared function
  // Always use full mode to get complete data (simplified mode disabled)
  const processStartTime = Date.now();
  const responseData = await postProcessPosts(
    postsData,
    auth_user_id,
    shouldFetchTranslations ? clientLanguage : undefined,
  );
  const processTime = Date.now() - processStartTime;

  const totalTime = Date.now() - startTime;
  console.log(
    `Query performance: ${queryTime}ms (query), ${processTime}ms (process), ${totalTime}ms (total), ${postsData.length} posts, full mode`,
  );

  // Cache the result
  setCache(cacheKey, responseData);

  return new Response(JSON.stringify(responseData), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  });
}
