/* eslint-disable */
import { createClient } from '@supabase/supabase-js';
import { serverUrl, TaskType, imageIndex } from '../tools/_constants.js';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { parse } from 'cookie';
import { decode } from 'next-auth/jwt';
import { fal } from '@fal-ai/client';

// Language mapping for translations
export const LANGUAGE_MAP: { [key: string]: string } = {
  zh: 'zh-CN',
  'zh-CN': 'zh-CN',
  'zh-TW': 'zh-TW',
  ja: 'ja',
  ko: 'ko',
  es: 'es',
  fr: 'fr',
  de: 'de',
  pt: 'pt',
  it: 'it',
  nl: 'nl',
  pl: 'pl',
  ru: 'ru',
  ar: 'ar',
  hi: 'hi',
  id: 'id',
  vi: 'vi',
  th: 'th',
  ms: 'ms',
  en: 'en',
};

// Language code to English name mapping (for API/LLM calls)
export const LANGUAGE_TO_ENGLISH_NAME: { [key: string]: string } = {
  'zh-CN': 'Chinese Simplified',
  'zh-TW': 'Chinese Traditional',
  ja: 'Japanese',
  ko: 'Korean',
  es: 'Spanish',
  fr: 'French',
  de: 'German',
  pt: 'Portuguese',
  it: 'Italian',
  nl: 'Dutch',
  pl: 'Polish',
  ru: 'Russian',
  ar: 'Arabic',
  vi: 'Vietnamese',
  th: 'Thai',
  ms: 'Malay',
  hi: 'Hindi',
  id: 'Indonesian',
  en: 'English',
};

// Language code to localized name mapping (for UI display)
export const LANGUAGE_TO_LOCAL_NAME: { [key: string]: string } = {
  'zh-CN': '简体中文',
  'zh-TW': '繁體中文',
  ja: '日本語',
  ko: '한국어',
  es: 'Español',
  fr: 'Français',
  de: 'Deutsch',
  pt: 'Português',
  it: 'Italiano',
  nl: 'Nederlands',
  pl: 'Polski',
  ru: 'Русский',
  ar: 'العربية',
  vi: 'Tiếng Việt',
  th: 'ไทย',
  ms: 'Bahasa Melayu',
  hi: 'हिंदी',
  id: 'Bahasa Indonesia',
  en: 'English',
};

// Supported languages list 
export const SUPPORTED_LANGUAGES = Object.keys(LANGUAGE_TO_LOCAL_NAME);

// UI language options (for dropdowns)
export const LANGUAGE_OPTIONS = SUPPORTED_LANGUAGES.map(code => ({
  code,
  name: LANGUAGE_TO_LOCAL_NAME[code],
  englishName: LANGUAGE_TO_ENGLISH_NAME[code],
}));

// Translation data types
export interface TranslationData {
  post_id: number;
  language: string;
  title: string;
  content: string;
}

export interface TranslationsMap {
  [postId: number]: { [language: string]: { title: string; content: string } };
}
export const getHistoryImageUrl = async (
  prompt_id: string,
  type: string,
): Promise<{ url: string; error: string }> => {
  const url = `${serverUrl}/history/${prompt_id}`;
  const response = await fetch(url);
  try {
    const data = await response.json();
    console.log('data', data, prompt_id);
    if (data?.[prompt_id]?.status?.status_str === 'error') {
      return { url: '', error: data[prompt_id].status.status_str };
    }
    if (data?.[prompt_id]?.outputs?.[imageIndex[type]]) {
      return {
        url:
          data[prompt_id].outputs[imageIndex[type]]?.['images']?.[0]?.[
            'filename'
          ] ?? '',
        error: '',
      };
    }
    return { url: '', error: '' };
  } catch (e) {
    console.error(e);
    return { url: '', error: '' };
  }
};

export const success = <T>(data: T) => {
  return new Response(JSON.stringify({ code: 1, message: 'success', data }), {
    status: 200,
  });
};

export const failed = (message: string, data?: any) => {
  return new Response(
    JSON.stringify({ code: 0, message, error: message, data }),
    { status: 200 },
  );
};
export const failedWithCode = (code: number, message: string, data?: any) => {
  return new Response(
    JSON.stringify({
      code: 0,
      message,
      error: message,
      data,
      error_code: code,
    }),
    { status: 200 },
  );
};

export const unauthorized = (message = 'Unauthorized') =>
  new Response(JSON.stringify({ error: message }), { status: 401 });

export const createSupabase = () =>
  createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  );

type HistoryResult = { prompt_id: string; filename: string; error?: string };
export const pollingHistory = (
  prompt_id: string,
  type: TaskType,
  timeout: number = 1000 * 60 * 3,
): Promise<HistoryResult> => {
  let running = true;
  let resolveHistory: (value: HistoryResult) => void;
  const historyPromise = new Promise<HistoryResult>((resolve, reject) => {
    resolveHistory = resolve;

    const fetchImage = async () => {
      try {
        const { url: imageUrl, error } = await getHistoryImageUrl(
          prompt_id,
          type,
        );
        if (error) {
          running = false;
          resolve({ prompt_id, filename: '', error: 'generate image failed' });
          return;
        }
        if (imageUrl) {
          running = false;
          resolve({ prompt_id, filename: imageUrl });
        }
      } catch (error) {
        console.error('Fetch image failed', error);
        resolve({ prompt_id, filename: '', error: 'Fetch image failed' });
        running = false;
      }
    };

    const perform = () => {
      if (!running) {
        return;
      }
      fetchImage();
      setTimeout(() => {
        perform();
      }, 3000);
    };

    perform();
  });

  return Promise.race<HistoryResult>([
    historyPromise,
    new Promise(resolve => {
      setTimeout(() => {
        resolve({ prompt_id, filename: '', error: 'generate image timeout' });
        resolveHistory({
          prompt_id,
          filename: '',
          error: 'generate image timeout',
        });
        console.error('generate image timeout', prompt_id);
      }, timeout);
    }),
  ]);
};

const fetchGoogleResult = async (op_name: string) => {
  const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;
  const BASE_URL = 'https://generativelanguage.googleapis.com/v1beta';
  const url = `${BASE_URL}/${op_name}?key=${GOOGLE_API_KEY}`;
  const response = await fetch(url);
  if (!response.ok) {
    return { error: 'Failed to fetch google result' };
  }
  const data = await response.json();
  console.log('data', data);
  return data;
};
interface GoogleResult {
  error?: string;
  response?: {
    generateVideoResponse: {
      generatedSamples: {
        video: {
          uri: string;
        };
      }[];
    };
  };
}
export const pollingGoogle = (
  op_name: string,
  timeout: number = 1000 * 60 * 3,
): Promise<GoogleResult> => {
  let running = true;
  let resolvePolling: (value: GoogleResult) => void;
  const pollingPromise = new Promise<any>((resolve, reject) => {
    resolvePolling = resolve;

    const fetchResult = async () => {
      try {
        const data = await fetchGoogleResult(op_name);
        if (data.error) {
          running = false;
          resolve({ error: data.error });
          return;
        }
        if (data.response) {
          running = false;
          resolve(data.response);
          return;
        }
        // If no response yet, continue polling
      } catch (error) {
        console.error('Fetch Google result failed', error);
        resolve({ error: 'Fetch Google result failed' });
        running = false;
      }
    };

    const perform = () => {
      if (!running) {
        return;
      }
      fetchResult();
      setTimeout(() => {
        perform();
      }, 5000); // Poll every 5 seconds
    };

    perform();
  });

  return Promise.race([
    pollingPromise,
    new Promise(resolve => {
      setTimeout(() => {
        running = false;
        resolvePolling({ error: 'Operation timeout' });
        console.error('Google operation timeout', op_name);
        resolve({ error: 'Operation timeout' });
      }, timeout);
    }),
  ]);
};

export async function dataURLToBlob(dataURL: string) {
	if (/^https?:/.test(dataURL)) {
    const response = await fetch(dataURL);
    const blob = await response.blob();
    return blob;
  }
  const [header, data] = dataURL.split(',');
  const contentType = header!.match(/:(.*?);/)![1];
	const byteCharacters = Buffer.from(data, 'base64');

  const blob = new Blob([byteCharacters], { type: contentType });
  return blob;
}

export async function uploadImage(imagePath: string, imageUri: string) {
  const supabase = createSupabase();
  // let imagePath = `image_generation/${userId}/${(new Date()).toISOString().split('T')[0]}-${uuidv4()}.webp`;
  let blob = await dataURLToBlob(imageUri);
  const supaResult = await supabase.storage
    .from('husbando-land')
    .upload(imagePath, blob);
  if (!supaResult.error) {
    const imageUrl = supabase.storage
      .from('husbando-land')
      .getPublicUrl(imagePath).data.publicUrl;
    // imageUrls.push(imageUrl);
    // imageUrlPath = imageUrl.replace(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/husbando-land/image_generation/`, "");
    return imageUrl;
  }
}

export const fetchGemini = async (prompt: string, images?: string[]) => {
  const API_KEY = process.env.GEMINI_API_KEY!;
  // const API_KEY = Math.random() > 0.5 ? 'AIzaSyA9iAYWptOWhllAWQ7IE3DMHWLgLFxFlaE' : 'AIzaSyDc17N7ac38TCa6ILQrYkhRTOl2S6mpWh4'
  const genAI = new GoogleGenerativeAI(API_KEY);
  const generationConfig = {
    temperature: 1,
    topP: 0.95,
    topK: 40,
    maxOutputTokens: 8192,
    responseMimeType: 'text/plain',
    responseModalities: ['image', 'text'],
  };

  // result.response.candidates![0].finishReason
  // result.response.candidates![0].content.parts
  const model = genAI.getGenerativeModel({
    model: 'gemini-2.0-flash-preview-image-generation',
    generationConfig,
  });
  const result = await model.generateContent(prompt);
  return result.response;
};

export const fetchGeminiNew = async (
  prompt: string,
  images?: string[],
): Promise<string> => {
  fal.config({
    credentials: process.env.fal_api_key,
  });
  const result = await fal.subscribe('fal-ai/gemini-flash-edit/multi', {
    input: {
      prompt,
      input_image_urls: images,
    },
    logs: true,
    onQueueUpdate: update => {
      if (update.status === 'IN_PROGRESS') {
        update.logs.map(log => log.message).forEach(console.log);
      }
    },
  });

  return result?.data?.image?.url || '';
};

export async function getAuthUserId(request: Request): Promise<string | null> {
  const cookies = parse(request.headers.get('cookie') || '');
  const sessionToken = cookies['next-auth.session-token'];

  if (!sessionToken) {
    return null;
  }

  try {
    const token = await decode({
      token: sessionToken,
      secret: process.env.NEXTAUTH_SECRET!,
    });
    if (!token) {
      return null;
    }
    return token.id as string;
  } catch (error) {
    console.error('Error decoding session token:', error);
    return null;
  }
}

export function hasError(
  err: any,
): err is { error: string; error_code?: number } {
  if ((err as any)?.error) {
    return true;
  }
  return false;
}

export const canGenerate = async ({
  userId,
  tool,
  model,
}: {
  userId: string;
  tool?: string;
  model?: string;
}) => {
  const supabase = createSupabase();
  const rpcName =
    process.env.NODE_ENV === 'development'
      ? 'try_generate_image_test'
      : 'try_generate_image';
  const { data, error } = await supabase.rpc(rpcName, {
    p_user_id: userId,
    p_tools: tool,
    p_model: model,
  });
  if (error) {
    console.error('Error checking rate limit:', error);
    return false;
  }
  return data as boolean;
};

export type MiddlewareResult = {
  success: boolean;
  response?: Response;
};

export type MiddlewareFunc = (request: Request) => Promise<MiddlewareResult>;

export const getUserId = (request: Request) => {
  return request.headers.get('x-user-id');
};

export const setToolModel = ({
  request,
  tool,
  model,
}: {
  request: Request;
  tool: string;
  model: string;
}) => {
  request.headers.set('x-generation-tool', tool);
  request.headers.set('x-generation-model', model);
};

// Helper function to detect file type from content type or file extension
export function getFileTypeCategory(
  contentType?: string,
  filename?: string,
): 'image' | 'video' | 'unknown' {
  if (contentType) {
    if (contentType.startsWith('image/')) return 'image';
    if (contentType.startsWith('video/')) return 'video';
  }

  if (filename) {
    const ext = filename.toLowerCase().split('.').pop();
    if (['jpg', 'jpeg', 'png', 'webp', 'gif', 'svg'].includes(ext || ''))
      return 'image';
    if (['mp4', 'webm', 'avi', 'mov', 'mkv', 'flv'].includes(ext || ''))
      return 'video';
  }

  return 'unknown';
}

// Helper function to generate file path based on type
export function generateFilePath(
  userId: string,
  fileType: 'image' | 'video',
  filename?: string,
): string {
  const timestamp = Date.now();
  const date = new Date().toISOString().split('T')[0];

  if (fileType === 'image') {
    const extension = filename ? filename.split('.').pop() : 'webp';
    return `image_generation/${userId}/${date}-${timestamp}.${extension}`;
  } else if (fileType === 'video') {
    const extension = filename ? filename.split('.').pop() : 'mp4';
    return `app_videos/${userId}/${date}-${timestamp}.${extension}`;
  }

  return `uploads/${userId}/${date}-${timestamp}`;
}

export async function uploadMediaFromUrl(
  url: string,
  userId: string,
  customPath?: string,
) {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch media from URL: ${response.statusText}`);
    }

    const contentType = response.headers.get('content-type');
    const arrayBuffer = await response.arrayBuffer();
    const blob = new Blob([arrayBuffer], { type: contentType || undefined });

    // Extract filename from URL if available
    const urlPath = new URL(url).pathname;
    const filename = urlPath.split('/').pop() || undefined;

    // Generate appropriate file path if not provided
    const filePath =
      customPath ||
      (() => {
        const fileType = getFileTypeCategory(
          contentType || undefined,
          filename,
        );
        if (fileType === 'unknown') {
          // Default to image if type is unknown
          return generateFilePath(userId, 'image', filename);
        }
        return generateFilePath(userId, fileType, filename);
      })();

    return await uploadMedia(filePath, blob);
  } catch (error) {
    console.error('Error uploading media from URL:', error);
    return null;
  }
}

export async function uploadMedia(
  filePath: string,
  mediaData: Blob | File | string,
) {
  const supabase = createSupabase();
  try {
    let blob: Blob;

    if (typeof mediaData === 'string') {
      // Check if it's a data URL or regular URL
      if (mediaData.startsWith('data:')) {
        // Handle data URL
        blob = await dataURLToBlob(mediaData);
      } else if (
        mediaData.startsWith('http://') ||
        mediaData.startsWith('https://')
      ) {
        // Handle regular URL - fetch the content
        const response = await fetch(mediaData);
        if (!response.ok) {
          throw new Error(`Failed to fetch from URL: ${response.statusText}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        const contentType = response.headers.get('content-type');
        blob = new Blob([arrayBuffer], { type: contentType || undefined });
      } else {
        throw new Error(
          'Invalid string format: must be data URL or HTTP/HTTPS URL',
        );
      }
    } else if (mediaData instanceof File || mediaData instanceof Blob) {
      blob = mediaData;
    } else {
      throw new Error('Invalid media data format');
    }

    const supaResult = await supabase.storage
      .from('husbando-land')
      .upload(filePath, blob, {
        cacheControl: '3600',
        upsert: false,
      });

    if (supaResult.error) {
      console.error('Error uploading to Supabase:', supaResult.error);
      return null;
    }

    const {
      data: { publicUrl },
    } = supabase.storage.from('husbando-land').getPublicUrl(filePath);

    return publicUrl;
  } catch (error) {
    console.error('Error in uploadMedia:', error);
    return null;
  }
}

export async function deleteMediaFromStorage(
  filePath: string | string[],
): Promise<{ success: boolean; error?: string; deletedFiles?: string[] }> {
  const supabase = createSupabase();

  try {
    const filePaths = Array.isArray(filePath) ? filePath : [filePath];

    const { data, error } = await supabase.storage
      .from('husbando-land')
      .remove(filePaths);

    if (error) {
      console.error('Error deleting files from Supabase storage:', error);
      return { success: false, error: error.message };
    }

    console.log('Successfully deleted files:', filePaths);
    return {
      success: true,
      deletedFiles: filePaths,
    };
  } catch (error) {
    console.error('Error in deleteMediaFromStorage:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function deleteMediaByUrl(
  publicUrl: string | string[],
): Promise<{ success: boolean; error?: string; deletedFiles?: string[] }> {
  try {
    const urls = Array.isArray(publicUrl) ? publicUrl : [publicUrl];
    const baseUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/husbando-land/`;

    // Extract file paths from public URLs
    const filePaths = urls.map(url => {
      if (url.startsWith(baseUrl)) {
        return url.replace(baseUrl, '');
      }
      throw new Error(`Invalid URL format: ${url}`);
    });

    return await deleteMediaFromStorage(filePaths);
  } catch (error) {
    console.error('Error in deleteMediaByUrl:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Invalid URL format',
    };
  }
}

export async function cleanupUserMedia(
  userId: string,
  mediaType?: 'image' | 'video',
): Promise<{ success: boolean; error?: string; deletedCount?: number }> {
  const supabase = createSupabase();

  try {
    let folderPaths: string[] = [];

    if (!mediaType || mediaType === 'image') {
      folderPaths.push(`image_generation/${userId}/`);
    }
    if (!mediaType || mediaType === 'video') {
      folderPaths.push(`app_videos/${userId}/`);
    }

    let totalDeleted = 0;
    const errors: string[] = [];

    for (const folderPath of folderPaths) {
      try {
        // List all files in the folder
        const { data: files, error: listError } = await supabase.storage
          .from('husbando-land')
          .list(folderPath);

        if (listError) {
          errors.push(
            `Error listing files in ${folderPath}: ${listError.message}`,
          );
          continue;
        }

        if (files && files.length > 0) {
          const filePaths = files.map(file => `${folderPath}${file.name}`);

          const { data, error: deleteError } = await supabase.storage
            .from('husbando-land')
            .remove(filePaths);

          if (deleteError) {
            errors.push(
              `Error deleting files in ${folderPath}: ${deleteError.message}`,
            );
          } else {
            totalDeleted += filePaths.length;
          }
        }
      } catch (folderError) {
        errors.push(`Error processing folder ${folderPath}: ${folderError}`);
      }
    }

    if (errors.length > 0) {
      console.error('Cleanup errors:', errors);
      return {
        success: false,
        error: errors.join('; '),
        deletedCount: totalDeleted,
      };
    }

    console.log(
      `Successfully cleaned up ${totalDeleted} files for user ${userId}`,
    );
    return {
      success: true,
      deletedCount: totalDeleted,
    };
  } catch (error) {
    console.error('Error in cleanupUserMedia:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export function replaceAll(str: string, search: string, replace: string) {
  while (str.includes(search)) {
    str = str.replace(search, replace);
  }
  return str;
}

export function replaceCharacterId(prompt: string) {
  const namePlaceholders = [
    'Alex',
    'Blair',
    'Caleb',
    'Dylan',
    'Ethan',
    'Finn',
    'George',
    'Henry',
    'Isaac',
  ];
  const placeholders = namePlaceholders.slice();
  const reg = /<([^>]+)>/g;
  const matched = prompt.match(reg);
  let finalPrompt = prompt;
  if (matched) {
    const uniqIds = [...new Set(matched)];
    uniqIds.forEach((uniqId, index) => {
      finalPrompt = replaceAll(finalPrompt, uniqId, placeholders[index]);
    });
  }
  return finalPrompt;
}

/**
 * Get the target language code based on client language
 */
export function getTargetLanguage(clientLanguage?: string): string {
  return LANGUAGE_MAP[clientLanguage || 'en'] || 'en';
}

/**
 * Build Supabase query for fetching translations
 * Always filters out empty translations to ensure data quality
 */
export function buildTranslationsQuery(
  supabase: any,
  postIds: number[],
  targetLanguage: string,
) {
  return supabase
    .from('PostTranslations')
    .select('post_id, language, title, content')
    .in('post_id', postIds)
    .eq('language', targetLanguage)
    .not('title', 'is', null)
    .not('content', 'is', null)
    .neq('title', '')
    .neq('content', '');
}
/**
 * Map translations data to a structured format
 */
export function mapTranslations(
  translationsData: TranslationData[] | null,
): TranslationsMap {
  if (!translationsData) {
    return {};
  }

  return translationsData.reduce((acc, translation) => {
    if (!acc[translation.post_id]) {
      acc[translation.post_id] = {};
    }
    acc[translation.post_id][translation.language] = {
      title: translation.title,
      content: translation.content,
    };
    return acc;
  }, {} as TranslationsMap);
}

/**
 * Check if posts array contains official account posts
 */
export function hasOfficialAccountPosts(
  postsData: any[],
  officialAccountId: string,
): boolean {
  return postsData.some(post => post.authUserId === officialAccountId);
}
