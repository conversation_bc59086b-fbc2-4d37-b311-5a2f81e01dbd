import Replicate from 'replicate';
import { fal } from '@fal-ai/client';
import { failed } from '../_utils/index.js';
import { getAuthUserId } from './image-generation.js';
import { CreditModel } from '../_models/credit.js';
import { calculateVideoToVideoStyleTransferCost, ToolsModel } from './_zaps.js';
import { authMiddleware } from '../_utils/middlewares/auth.js';
import {
  bindGenerationLogData,
  bindParamsMiddleware,
} from '../_utils/middlewares/index.js';
import withHandler from '../_utils/withHandler.js';
import { tryGenerateHandler } from '../_utils/middlewares/tryGenerate.js';
import { translateMiddleware } from '../_utils/middlewares/translate.js';
import { GenerationStatus, TASK_TYPES } from '../_constants.js';
import { generateImageFluxKontextPro } from '../generateImageFluxKontextPro.js';

// Configure Replicate client
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// Configure fal.ai client
fal.config({
  credentials: process.env.fal_api_key,
});

// Style template mappings with categories
const getStyleInfo = (style: string) => {
  const styleData: {
    [key: string]: {
      prompt: string;
      changeType?: string;
    };
  } = {
    // Cosplay
    barbie: {
      prompt:
        'Change the clothing and hairstyle of the actor so that he/she dresses like the iconic character Barbie doll.',
    },
    miku: {
      prompt:
        'Make the actor dress in character costume as if they are cosplaying the iconic character Hatsune Miku.',
      changeType: 'cosplay',
    },
    naruto: {
      prompt:
        'Change the clothing and hairstyle of the actor so that he/she dresses like the iconic character Naruto',
    },
    'sailor-moon': {
      prompt:
        'Change the clothing and hairstyle of the actor so that he/she dresses like the iconic character Sailor Moon.',
    },
    goku: {
      prompt:
        'Make the actor dress in character costume as if they are cosplaying the iconic anime character Goku.',
      changeType: 'cosplay',
    },
    princess: {
      prompt:
        'Change the clothing of the actor so that he/she dresses like a prince/princess',
    },
    kimono: {
      prompt:
        "Change the character's clothing into a traditional Japanese kimono or summer yukata",
      changeType: 'clothing',
    },
    superhero: {
      prompt: "Transform the character's clothes into a superhero outfit",
      changeType: 'outfit and style',
    },
    'magical-girl': {
      prompt:
        'Make the actor appear as if they are cosplaying an iconic magical girl from anime — keep the actor’s face and features, but dress them in a vibrant, sparkly magical girl costume with a short frilly skirt, bows, gloves, and knee-high boots',
      changeType: 'outfit and style',
    },
    hogwarts: {
      prompt: "Change the character's outfit into a Hogwarts school uniform",
      changeType: 'outfit',
    },
    cowboy: {
      prompt: 'Change the outfit to a Wild West cowboy look',
      changeType: 'outfit',
    },
    'sailor-uniform': {
      prompt: 'Change his/her clothing to Japanese sailor uniform',
    },

    // Style
    anime: {
      prompt: 'Japanese anime style',
      changeType: 'style',
    },
    'ghibli-anime': {
      prompt: 'Studio Ghibli anime art style',
      changeType: 'style',
    },
    'korean-manhwa': {
      prompt: 'Korean manhwa style',
      changeType: 'style',
    },
    cartoon: {
      prompt: 'cartoon style',
      changeType: 'style',
    },
    manga: {
      prompt: 'Japanese manga style',
      changeType: 'style',
    },
    clay: {
      prompt: 'claymation style',
      changeType: 'style',
    },
    vaporwave: {
      prompt: 'vaporwave style',
      changeType: 'style',
    },
    comic: {
      prompt: 'American comic style',
      changeType: 'style',
    },
    pixar: {
      prompt: 'Convert the image into Pixar-style 3D animation',
      changeType: 'style',
    },
    'pencil-sketch': {
      prompt:
        'Make the image a monochromatic pencil sketch with cross-hatching for shading',
      changeType: 'style',
    },
    'retro-game': {
      prompt:
        'Transform into a bright color pixel art video game interface, inspired by arcade or SNES-style graphics. Use large, chunky pixels with bold outlines and flat color. Include dynamic game UI elements such as health bar, map, score counters, inventory slots, control buttons, timer, speedometer',
      changeType: 'style',
    },
    'mobile-game': {
      prompt:
        'Transform this scene into a mobile game interface with a clean and cartoon art style. Use stylized graphics with smooth shading. Add well-designed large UI elements and large game buttons suitable for this game',
      changeType: 'style',
    },
    'ps-game': {
      prompt: 'Turn the scene into a PlayStation game with game UI and buttons',
      changeType: 'style',
    },
    'western-animation': {
      prompt: 'Turn the image into American 2d animation style',
      changeType: 'style',
    },
    watercolor: {
      prompt: 'Turn the image into watercolor style',
      changeType: 'style',
    },
    'van-gogh': {
      prompt: 'Turn the scene into a Van Gogh illustration.',
      changeType: 'style',
    },
    'oil-painting': {
      prompt: 'Turn the scene into an oil painting',
      changeType: 'style',
    },

    // Change Environment
    apocalypse: {
      prompt:
        'Change the background environment to a post-apocalyptic wasteland — destroyed buildings, dust clouds, and eerie lighting',
      changeType: 'background',
    },
    // 'magical-world': {
    //   prompt: 'change the background to a whimsical magical world',
    //   changeType: 'background',
    // },
    // dreamland: {
    //   prompt: 'Change the environment to a surreal dreamscape',
    //   changeType: 'environment',
    // },
    cyberpunk: {
      prompt: 'change the background to a neon-lit cyberpunk environment',
      changeType: 'background',
    },
    'kpop-idol': {
      prompt:
        'Transform into a glamorous K-pop idol performing live on stage. Surround them with dynamic concert lighting: colorful spotlights, backlighting, and stage glow. Add subtle makeup, glowing skin, and styled hair suited for a K-pop performance. Maintain the same pose and body position',
      changeType: 'environment',
    },
    cloud: {
      prompt:
        'Make main human/object appear to be on soft, fluffy clouds in the sky. Keep the pose and image composition exactly the same as the input image',
      changeType: 'background',
    },
    underwater: {
      prompt:
        'Reimagine the entire scene as if it takes place underwater. Light rays filtering through the surface above. Include swimming fish, drifting sea grass, and soft bubbles rising through the water. Make sure the scene is bright enough to be clearly visible',
      changeType: 'background',
    },
    mars: {
      prompt:
        'Change the environment to the surface of Mars — place all humans and objects on the red, rocky Martian terrain with dusty ground, scattered boulders, and distant hills. Add a reddish-orange sky with subtle atmospheric haze. Replace any buildings or structures from the background of the original image with scifi human settlements on Mars.',
      changeType: 'background',
    },
    'outer-space': {
      prompt:
        'Change the environment to outer space — place all humans and objects in a zero-gravity setting, with extremely massive giant planets, spacecraft, and spaceships visible in background. Remove any ground or floor elements. Instead of a pitch-black background, add enough sci-fi lighting so the scene is bright enough to be visible.',
      changeType: 'background',
    },
    snow: {
      prompt:
        'Turn it into a snowing scene where snow is visible everywhere and large snow covering the ground.',
      changeType: 'background',
    },

    // Change Material
    'toy-bricks': {
      prompt: 'Turn the characters and scenes into Lego toy bricks',
      changeType: 'material',
    },
    skeleton: {
      prompt:
        'Turn the characters into skeleton with pose same as the original image',
      changeType: 'material',
    },
    fire: {
      prompt:
        'Reimagine the entire scene with all characters made of fire, keep the environment unchanged',
      changeType: 'material',
    },
    muscle: {
      prompt:
        'Turn the character into a anatomical muscle model with muscle and tendon and sinew all exposed. Keep the character identity, facial features, clothes and pose the same as the original image',
      changeType: 'material',
    },
    metal: {
      prompt:
        'Reimagine the entire scene with all characters and objects made of bright metal',
      changeType: 'material',
    },
    crystal: {
      prompt:
        'Reimagine the entire scene with all characters and animals made of semi-transparent bright and shiny crystal with pose same as the original image',
      changeType: 'material',
    },
  };

  const result = styleData[style] || {
    prompt: `${style.toLowerCase()} style`,
    changeType: 'style',
  };

  return result;
};

// Fallback function using fal.ai
async function fallbackToFalAI(
  requestId: string,
  userId: string,
  finalPrompt: string,
  image_url: string,
  cost: number,
): Promise<string> {
  console.log(
    `[${requestId}] Attempting fallback to fal.ai for user ${userId}`,
  );

  const falModelName = 'fal-ai/step1x-edit';

  const falInput = {
    prompt: finalPrompt,
    image_url,
    enable_safety_checker: false,
  };

  console.log(
    `[${requestId}] Calling fal.ai model ${falModelName} for user ${userId}`,
  );

  const result = await fal.subscribe(falModelName, {
    input: falInput,
    logs: true,
    onQueueUpdate: update => {
      if (update.status === 'IN_PROGRESS') {
        console.log(
          `[${requestId}] fal.ai processing in progress for user ${userId}`,
        );
      }
    },
  });

  if (!result.data || !result.data.images || !result.data.images[0]) {
    console.error(
      `[${requestId}] No image data returned from fal.ai:`,
      result.data,
    );
    throw new Error('Failed to generate styled image with fallback model');
  }

  console.log(`[${requestId}] fal.ai generation successful for user ${userId}`);
  return result.data.images[0].url;
}

// Helper function to parse Replicate errors
function parseReplicateError(error: any) {
  // Check for specific error codes in the message
  const errorMessage = error?.message || error?.toString() || 'Unknown error';

  // E005 - Content flagged as sensitive
  if (
    errorMessage.includes('E005') ||
    errorMessage.includes('flagged as sensitive')
  ) {
    return {
      userMessage:
        'Your content was flagged as potentially sensitive. Please try with different inputs or a different style.',
      errorCode: 'E005',
      category: 'CONTENT_FILTER',
    };
  }

  // Rate limit errors
  if (errorMessage.includes('rate limit') || errorMessage.includes('429')) {
    return {
      userMessage:
        'Service is currently busy. Please try again in a few moments.',
      errorCode: 'RATE_LIMIT',
      category: 'RATE_LIMIT',
    };
  }

  // Model loading errors
  if (
    errorMessage.includes('model is loading') ||
    errorMessage.includes('starting up')
  ) {
    return {
      userMessage:
        'The AI model is starting up. Please wait a moment and try again.',
      errorCode: 'MODEL_LOADING',
      category: 'MODEL_LOADING',
    };
  }

  // Input validation errors
  if (
    errorMessage.includes('invalid input') ||
    errorMessage.includes('validation')
  ) {
    return {
      userMessage:
        'Invalid input parameters. Please check your image URL and settings.',
      errorCode: 'INVALID_INPUT',
      category: 'VALIDATION',
    };
  }

  // Default case - return original message if it's user-friendly, otherwise generic message
  const isUserFriendly =
    !errorMessage.includes('Error:') &&
    !errorMessage.includes('Exception') &&
    errorMessage.length < 200;

  return {
    userMessage: isUserFriendly
      ? errorMessage
      : 'Failed to process your request. Please try again.',
    errorCode: 'UNKNOWN',
    category: 'UNKNOWN',
    originalMessage: errorMessage,
  };
}

async function handler(request: Request) {
  const requestId = `vvst_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    console.log(`[${requestId}] Video-to-video style transfer request started`);

    // 1. Authentication
    const userId = await getAuthUserId(request);

    if (!userId) {
      console.log(`[${requestId}] Unauthorized request`);
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    console.log(`[${requestId}] User authenticated: ${userId}`);

    // 2. Parse parameters
    const params = (request as any).params;

    if (!params) {
      console.error(`[${requestId}] Invalid params for user ${userId}`);
      return failed('Invalid params');
    }

    const {
      image_url,
      user_prompt,
      originalPrompt,
      mode,
      reference_image_url,
      model = ToolsModel.BASIC,
    } = params;

    console.log(`[${requestId}] Request params for user ${userId}:`, {
      mode,
      hasImageUrl: !!image_url,
      hasUserPrompt: !!user_prompt,
      hasReferenceImage: !!reference_image_url,
      model,
      userPrompt: user_prompt,
      originalPrompt,
    });

    // 3. Validate required parameters
    if (!image_url) {
      console.error(`[${requestId}] Missing image_url for user ${userId}`);
      return failed('Image URL is required');
    }

    if (!user_prompt && mode !== 'upload') {
      console.error(
        `[${requestId}] Missing user_prompt for mode ${mode}, user ${userId}`,
      );
      return failed('User prompt is required for template and custom modes');
    }

    if (mode === 'upload' && !reference_image_url) {
      console.error(
        `[${requestId}] Missing reference_image_url for upload mode, user ${userId}`,
      );
      return failed('Reference image is required for upload mode');
    }

    // 4. Calculate cost
    const cost = calculateVideoToVideoStyleTransferCost(mode);
    console.log(
      `[${requestId}] Calculated cost: ${cost} for mode: ${mode}, user: ${userId}`,
    );

    // Bind generation log data
    const initialModelName = 'FLUX.1 Kontext Pro';
    bindGenerationLogData(request, {
      model: initialModelName,
      generationResult: GenerationStatus.FAILED,
      tool: 'video-to-video-style-transfer',
      cost,
    });

    // 5. Check user credits (only for template and custom modes)
    if (mode !== 'upload') {
      console.log(
        `[${requestId}] Checking credits for user ${userId}, required: ${cost}`,
      );
      const credit = new CreditModel(userId);
      const success = await credit.canConsume(cost);
      if (!success) {
        console.error(
          `[${requestId}] Insufficient credits for user ${userId}, required: ${cost}`,
        );
        return failed('Insufficient credits');
      }
      console.log(`[${requestId}] Credit check passed for user ${userId}`);
    }

    // 6. Build prompt based on mode
    let finalPrompt = '';
    const sizePrompt =
      'Keep the image composition, image structure, aspect ratio, and exact dimensions identical to the input image';
    switch (mode) {
      case 'template':
        const styleInfo = getStyleInfo(user_prompt);
        if (styleInfo.changeType) {
          finalPrompt = `${styleInfo.prompt}. ${sizePrompt}, only change the ${styleInfo.changeType}`;
        } else {
          finalPrompt = `${styleInfo.prompt}. ${sizePrompt}`;
        }
        break;
      case 'custom':
        finalPrompt = `${user_prompt}. ${sizePrompt}`;
        console.log(`[${requestId}] Custom mode - User prompt: ${user_prompt}`);
        break;
      case 'upload':
        finalPrompt =
          'Transform the image to match the style of the reference image. Keep the objects, characters, image composition, image structure, aspect ratio, and exact dimensions identical to the input image, only change the artistic style to match the reference.';
        console.log(
          `[${requestId}] Upload mode - Using reference image: ${reference_image_url}`,
        );
        break;
      default:
        console.error(
          `[${requestId}] Invalid mode specified: ${mode} for user ${userId}`,
        );
        return failed('Invalid mode specified');
    }

    console.log(
      `[${requestId}] Final prompt for user ${userId}: ${finalPrompt}`,
    );

    // 7. Handle upload mode (direct return)
    if (mode === 'upload' && reference_image_url) {
      console.log(
        `[${requestId}] Upload mode - returning reference image directly for user ${userId}`,
      );

      // Update generation log
      bindGenerationLogData(request, {
        model: 'reference-upload',
        generationResult: GenerationStatus.SUCCEEDED,
        tool: 'video-to-video-style-transfer',
        cost: 0,
      });

      const response = {
        output: reference_image_url,
        request_id: requestId,
      };

      console.log(
        `[${requestId}] Upload mode completed successfully for user ${userId}`,
      );
      return new Response(JSON.stringify(response), { status: 200 });
    }

    // 8. Prepare Replicate input for template and custom modes
    const replicateInput: any = {
      prompt: finalPrompt,
      input_image: image_url,
      safety_tolerance: 6,
    };

    // Add aspect_ratio if provided
    if (params.aspect_ratio) {
      replicateInput.aspect_ratio = params.aspect_ratio;
      console.log(
        `[${requestId}] Added aspect_ratio: ${params.aspect_ratio} for user ${userId}`,
      );
    }

    console.log(`[${requestId}] Replicate input for user ${userId}:`, {
      prompt: finalPrompt,
      input_image: image_url,
      safety_tolerance: 6,
      aspect_ratio: params.aspect_ratio,
    });

    // 9. Try Replicate first, then fallback to fal.ai
    const replicateModelName = 'black-forest-labs/flux-kontext-pro';
    console.log(
      `[${requestId}] Calling Replicate model ${replicateModelName} for user ${userId}`,
    );

    let output;
    let usedModel = replicateModelName;
    let fallbackUsed = false;

    try {
      // output = await replicate.run(replicateModelName, {
      //   input: replicateInput,
      // });
      output = await generateImageFluxKontextPro({
        prompt: finalPrompt,
        image: image_url,
        aspectRatio: params.aspect_ratio,
      });
      console.log(
        `[${requestId}] Replicate call successful for user ${userId}, output type: ${typeof output}`,
      );
    } catch (replicateError: any) {
      console.error(`[${requestId}] Replicate API error for user ${userId}:`, {
        message: replicateError?.message,
        status: replicateError?.status,
        details: replicateError?.detail || replicateError?.details,
        fullError: replicateError,
      });

      console.log(
        `[${requestId}] Attempting fallback to fal.ai for user ${userId}`,
      );
      try {
        output = await fallbackToFalAI(
          requestId,
          userId,
          finalPrompt,
          image_url,
          cost,
        );
        usedModel = 'fal-ai/step1x-edit';
        fallbackUsed = true;
        console.log(
          `[${requestId}] Fallback to fal.ai successful for user ${userId}`,
        );
      } catch (fallbackError: any) {
        console.error(
          `[${requestId}] Fallback to fal.ai also failed for user ${userId}:`,
          fallbackError,
        );

        const parsedError = parseReplicateError(replicateError);
        console.log(
          `[${requestId}] Parsed error for user ${userId}:`,
          parsedError,
        );

        return new Response(
          JSON.stringify({
            error: `${parsedError.userMessage} (Fallback also failed)`,
            errorCode: parsedError.errorCode,
            category: parsedError.category,
            requestId,
          }),
          { status: 400 },
        );
      }
    }

    // 10. Validate output
    if (!output) {
      console.error(
        `[${requestId}] No image returned from ${usedModel} for user ${userId}`,
      );
      return failed('Failed to generate styled image - no output received');
    }

    console.log(
      `[${requestId}] Generation successful for user ${userId} using ${usedModel}, output: ${typeof output === 'string' ? `${output.substring(0, 100)}...` : output}`,
    );

    // 11. Deduct credits (only for template and custom modes)
    console.log(`[${requestId}] Deducting ${cost} credits for user ${userId}`);
    const credit = new CreditModel(userId);
    const deductResult = await credit.deductCredit(cost);
    if (!deductResult) {
      console.error(
        `[${requestId}] Failed to deduct credits for user ${userId}`,
      );
      return failed('Failed to deduct credits');
    }
    console.log(
      `[${requestId}] Credits deducted successfully for user ${userId}`,
    );

    // Use the generated URL directly
    const outputImageUrl = output;

    // 12. Update generation log
    bindGenerationLogData(request, {
      model: usedModel,
      generationResult: GenerationStatus.SUCCEEDED,
      tool: 'video-to-video-style-transfer',
      cost,
      fallbackUsed,
    });

    const response = {
      output: outputImageUrl,
      request_id: requestId,
    };

    console.log(
      `[${requestId}] Style transfer completed successfully for user ${userId}`,
    );
    return new Response(JSON.stringify(response), { status: 200 });
  } catch (error: any) {
    console.error(`[${requestId}] Unexpected error in style transfer:`, {
      message: error?.message,
      stack: error?.stack,
      error,
    });

    const parsedError = parseReplicateError(error);
    console.log(`[${requestId}] Parsed unexpected error:`, parsedError);

    return new Response(
      JSON.stringify({
        error: parsedError.userMessage,
        errorCode: parsedError.errorCode,
        category: parsedError.category,
        requestId,
      }),
      { status: 500 },
    );
  }
}

export const POST = withHandler(handler, [
  authMiddleware,
  bindParamsMiddleware,
  translateMiddleware,
  tryGenerateHandler(TASK_TYPES.IMAGE),
]);
