import { createClient } from "@supabase/supabase-js";
import { parse } from "cookie";
import { decode } from "next-auth/jwt";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { LANGUAGE_TO_ENGLISH_NAME, SUPPORTED_LANGUAGES } from './_utils/index.js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// 初始化Gemini AI
const gemini_key = process.env.GEMINI_API_KEY;
if (!gemini_key) {
	throw new Error("Gemini API key is not set");
}
const genAI = new GoogleGenerativeAI(gemini_key);

const generationConfig = {
	temperature: 1,
	topP: 0.95,
	topK: 40,
	maxOutputTokens: 8192,
	responseMimeType: 'text/plain',
	responseModalities: ['text']
};

const model = genAI.getGenerativeModel({
	model: "gemini-2.0-flash",
	generationConfig
});

// 使用统一的语言常量
const langMap = LANGUAGE_TO_ENGLISH_NAME;
const lang = SUPPORTED_LANGUAGES;

// 官方账号ID
const OFFICIAL_ACCOUNT_ID = process.env.OFFICIAL_ACCOUNT_ID || "";

interface TranslationRequest {
	postId: number;
	forceRetranslate?: boolean;
}

interface TranslationResponse {
	postId: number;
	translations: {
		language: string;
		title: string;
		content: string;
		success: boolean;
		error?: string;
	}[];
}

// 添加延迟函数
function delay(ms: number): Promise<void> {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

// 添加重试逻辑的翻译函数
async function translateText(
	text: string,
	targetLang: string,
	maxRetries = 3,
): Promise<string> {
	const languageName = langMap[targetLang as keyof typeof langMap];
	if (!languageName) {
		throw new Error(`Unsupported language: ${targetLang}`);
	}

	const prompt = `将以下文本翻译为${languageName}，保持原文的意思和风格。翻译后的文案最好和英文文案保持相似的长度。要求只返回翻译结果，不要添加任何解释或注释：

原文：
${text}

翻译：`;

	let lastError: Error | null = null;

	for (let attempt = 1; attempt <= maxRetries; attempt++) {
		try {
			console.log(
				`Translation attempt ${attempt}/${maxRetries} for ${targetLang}`,
			);

					// 为每次尝试添加超时
		const timeoutPromise = new Promise((_, reject) => {
			setTimeout(() => reject(new Error("Translation timeout")), 60000); // 60秒超时
		});

			const translationPromise = (async () => {
				const result = await model.generateContent(prompt);
				const response = await result.response;
				return response.text().trim();
			})();

			const translatedText = (await Promise.race([
				translationPromise,
				timeoutPromise,
			])) as string;

			if (translatedText && translatedText.length > 0) {
				console.log(
					`Translation successful for ${targetLang} on attempt ${attempt}`,
				);
				return translatedText;
			} else {
				throw new Error("Empty translation result");
			}
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));
			console.error(
				`Translation attempt ${attempt}/${maxRetries} failed for ${targetLang}:`,
				lastError.message,
			);

			if (attempt < maxRetries) {
				// 指数退避：第一次重试等待1秒，第二次重试等待2秒
				const waitTime = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
				console.log(`Waiting ${waitTime}ms before retry...`);
				await delay(waitTime);
			}
		}
	}

	console.error(`All translation attempts failed for ${targetLang}`);
	throw (
		lastError || new Error(`Translation failed after ${maxRetries} attempts`)
	);
}

async function translatePostContent(
	title: string,
	content: string,
	languages: string[],
): Promise<
	{
		language: string;
		title: string;
		content: string;
		success: boolean;
		error?: string;
	}[]
> {
	const results: {
		language: string;
		title: string;
		content: string;
		success: boolean;
		error?: string;
	}[] = [];

	console.log(`Starting translation for ${languages.length} languages`);

	for (let i = 0; i < languages.length; i++) {
		const lang = languages[i];
		console.log(`Processing language ${i + 1}/${languages.length}: ${lang}`);

		try {
			// 翻译标题
			const translatedTitle = await translateText(title, lang);

			// 翻译内容（如果有）
			const translatedContent = content
				? await translateText(content, lang)
				: "";

			results.push({
				language: lang,
				title: translatedTitle,
				content: translatedContent,
				success: true,
			});

			console.log(`✅ Successfully translated to ${lang}`);

			// 在处理下一个语言之前添加延迟以避免速率限制
			if (i < languages.length - 1) {
				console.log("Waiting 1 second before next language...");
				await delay(1000);
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Translation failed";
			console.error(`❌ Failed to translate to ${lang}: ${errorMessage}`);

			results.push({
				language: lang,
				title: "",
				content: "",
				success: false,
				error: errorMessage,
			});

			// 即使失败也要等待，避免连续的API调用
			if (i < languages.length - 1) {
				console.log("Waiting 0.5 seconds before next language (after error)...");
				await delay(500);
			}
		}
	}

	const successCount = results.filter((r) => r.success).length;
	console.log(
		`Translation completed: ${successCount}/${languages.length} successful`,
	);

	return results;
}

export async function POST(request: Request) {
	try {
		const {
			postId,
			forceRetranslate = false,
		}: TranslationRequest = await request.json();

		// 验证认证
		const cookies = parse(request.headers.get("cookie") || "");
		const sessionToken = cookies["next-auth.session-token"];

		if (!sessionToken) {
			return new Response(
				JSON.stringify({ error: "Authentication required" }),
				{
					status: 401,
					headers: { "Content-Type": "application/json" },
				},
			);
		}

		const token = await decode({
			token: sessionToken,
			secret: process.env.NEXTAUTH_SECRET!,
		});

		if (!token || !token.id) {
			return new Response(JSON.stringify({ error: "Invalid authentication" }), {
				status: 401,
				headers: { "Content-Type": "application/json" },
			});
		}

		const userId = token.id as string;

		// 检查是否为官方账号
		if (userId !== OFFICIAL_ACCOUNT_ID) {
			return new Response(
				JSON.stringify({
					error: "Unauthorized: Only official account can translate posts",
				}),
				{
					status: 403,
					headers: { "Content-Type": "application/json" },
				},
			);
		}

		// 验证post存在
		const { data: post, error: postError } = await supabase
			.from("AppPosts")
			.select("id, title, content, authUserId")
			.eq("id", postId)
			.single();

		if (postError || !post) {
			return new Response(JSON.stringify({ error: "Post not found" }), {
				status: 404,
				headers: { "Content-Type": "application/json" },
			});
		}

		// 检查是否已经翻译过
		if (!forceRetranslate) {
			const { data: existingTranslations } = await supabase
				.from("PostTranslations")
				.select("language")
				.eq("post_id", postId)
				.in("language", lang);

			if (
				existingTranslations &&
				existingTranslations.length === lang.length
			) {
				return new Response(
					JSON.stringify({
						message: "Post already translated",
						translations: existingTranslations,
					}),
					{
						status: 200,
						headers: { "Content-Type": "application/json" },
					},
				);
			}
		}

		// 执行翻译
		console.log(
			`Starting translation for post ${postId} with ${lang.length} languages`,
		);
		const startTime = Date.now();

		const translationResults = await translatePostContent(
			post.title,
			post.content,
			lang,
		);

		const endTime = Date.now();
		const duration = Math.round((endTime - startTime) / 1000);
		const successCount = translationResults.filter((r) => r.success).length;

		console.log(
			`Translation completed for post ${postId}: ${successCount}/${lang.length} successful in ${duration}s`,
		);

		// 存储翻译结果
		const translationsToInsert = translationResults
			.filter((result) => result.success)
			.map((result) => ({
				post_id: postId,
				language: result.language,
				title: result.title,
				content: result.content,
			}));

		if (translationsToInsert.length > 0) {
			// 如果强制重新翻译，先删除现有翻译
			if (forceRetranslate) {
				await supabase
					.from("PostTranslations")
					.delete()
					.eq("post_id", postId)
					.in("language", lang);
			}

			// 插入新翻译
			const { error: insertError } = await supabase
				.from("PostTranslations")
				.upsert(translationsToInsert, {
					onConflict: "post_id,language",
				});

			if (insertError) {
				console.error("Error inserting translations:", insertError);
				return new Response(
					JSON.stringify({ error: "Failed to save translations" }),
					{
						status: 500,
						headers: { "Content-Type": "application/json" },
					},
				);
			}
		}

		return new Response(
			JSON.stringify({
				message: "Translation completed",
				postId: postId,
				summary: {
					totalLanguages: lang.length,
					successful: successCount,
					failed: translationResults.length - successCount,
					duration: `${duration}s`,
				},
				translations: translationResults,
			}),
			{
				status: 200,
				headers: { "Content-Type": "application/json" },
			},
		);
	} catch (error) {
		console.error("Translation error:", error);
		return new Response(JSON.stringify({ error: "Internal server error" }), {
			status: 500,
			headers: { "Content-Type": "application/json" },
		});
	}
}

// GET方法：获取post的翻译
export async function GET(request: Request) {
	try {
		const url = new URL(request.url);
		const postId = url.searchParams.get("postId");
		const language = url.searchParams.get("language");

		if (!postId) {
			return new Response(JSON.stringify({ error: "postId is required" }), {
				status: 400,
				headers: { "Content-Type": "application/json" },
			});
		}

		let query = supabase
			.from("PostTranslations")
			.select("*")
			.eq("post_id", postId);

		if (language) {
			query = query.eq("language", language);
		}

		const { data, error } = await query;

		if (error) {
			console.error("Error fetching translations:", error);
			return new Response(
				JSON.stringify({ error: "Failed to fetch translations" }),
				{
					status: 500,
					headers: { "Content-Type": "application/json" },
				},
			);
		}

		return new Response(JSON.stringify({ translations: data || [] }), {
			status: 200,
			headers: { "Content-Type": "application/json" },
		});
	} catch (error) {
		console.error("Get translations error:", error);
		return new Response(JSON.stringify({ error: "Internal server error" }), {
			status: 500,
			headers: { "Content-Type": "application/json" },
		});
	}
}
