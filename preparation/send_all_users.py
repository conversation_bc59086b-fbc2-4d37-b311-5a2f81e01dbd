import os
import resend
import pandas as pd

resend.api_key = "re_9Rv4YMZE_4W4ahgXHnfEnGdFzeJoA13RQ"

# Load the CSV file
file_path = 'preparation/test_users.csv'
# file_path = 'preparation/all_users.csv'
# file_path = 'preparation/cpp.csv'
data = pd.read_csv(file_path)

# Select the 'name' and 'email' columns
selected_columns = data[['name', 'email']]
for index, row in selected_columns.iterrows():
    # if index < 37:
    #     continue
    username = row['name']
    email = row['email']
    if pd.isna(username):
        continue
    params: resend.Emails.SendParams = {
        "sender": "Ko<PERSON>ko<<EMAIL>>",
        "to": [email],
        "subject": "🎥 Submit your AI short anime + win up to $3K and free AI tool subscriptions!",
        "html": f"""<!doctype html>
<html>
<head>
<meta charset='UTF-8'><meta name='viewport' content='width=device-width initial-scale=1'>
<title></title>
</head>
<body><p>Dear {username if username else 'KomikoAI Fam'},</p>
<p>Ever imagined your characters coming alive through music? 🎶 Now&#39;s your chance—our <strong>AI AMV Challenge</strong> is officially LIVE!</p>
<p>Here’s how to enter (it’s super easy and fun!):</p>
<ol>
<li>Create anime video clips using KomikoAI’s <a href='https://komiko.app/video-to-video'>Video Generator</a>.</li>
<li>Make an Anime Music Video (AMV) at least <strong>15 seconds long</strong>, featuring your OC and your favorite track.</li>
<li>Post your AMV in the <a href='https://komiko.app/publish'>KomikoAI community</a> <strong>AND</strong> on <strong>any social media</strong> <strong>platform —</strong> X (Twitter), TikTok, Instagram</li>
<li>Make sure to <strong>tag #AMVchallenge and @KomikoAI</strong>.</li>

</ol>
<h2 id='🏆-prizes-up-for-grabs'>🏆 Prizes up for grabs:</h2>
<ul>
<li>Most liked on <a href='https://komiko.app/home'>KomikoAI Community</a>: <strong>$100 cash + 3-month KomikoAI Plus subscription</strong></li>
<li>Most liked on social media platfom: <strong>$100 cash + 3-month KomikoAI Plus subscription</strong></li>
<li>Outstanding entries (x5): <strong>1-month KomikoAI Plus subscription</strong></li>

</ul>
<p>⏰ <strong>Dates:</strong> July 30 – August 12, 2025</p>
<p><a href='https://komiko.app/home'><strong>👉 Join the Challenge now!</strong></a></p>
<p>P.S. Not sure how to get started? Don&#39;t worry—our official step-by-step AMV tutorial blog post is coming soon. Stay tuned!</p>
<p>Can’t wait to see your amazing AMVs! 🎬</p>
<p>Cheers, </p>
<p>The KomikoAI Team</p>
</body>
</html>""",
        #   "html": "<strong>hello, world!</strong>",
        "reply_to": "<EMAIL>",
        "headers": {
            "X-Entity-Ref-ID": "123456789"
        },
    }

    try:
        email = resend.Emails.send(params)
        print(f"邮件已成功发送给 {username} ({email})")
    except Exception as e:
        print(f"发送邮件给 {username} 时出错: {str(e)}")
        # 可选：打印响应内容以便调试
        try:
            print(f"响应内容: {email}")  # 如果能访问到响应对象
        except:
            pass
        continue  # 继续处理下一个收件人